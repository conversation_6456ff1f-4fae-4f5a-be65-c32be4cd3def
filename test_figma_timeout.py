#!/usr/bin/env python3
"""
Test script to verify the Figma screen processing timeout functionality.
This script simulates a request to the writing_screen_json_files endpoint
and verifies that it returns within 60 seconds with proper timeout handling.
"""

import asyncio
import httpx
import time
import json
from typing import Dict, Any

# Configuration
API_BASE_URL = "http://localhost:8000"  # Adjust based on your setup
ENDPOINT = "/api/figma/writing_screen_json_files"

# Test data - replace with actual values from your system
TEST_DATA = {
    "project_id": "test_project_123",
    "screens": {
        "figma_id": "T1234-P5678-test123",
        "selected_screen_ids": ["1:123", "1:456", "1:789"]  # Add more to increase processing time
    },
    "task_id": "test_task_123"
}

# Headers - you'll need to add proper authentication
HEADERS = {
    "Content-Type": "application/json",
    "Authorization": "Bearer YOUR_JWT_TOKEN_HERE"  # Replace with actual token
}

async def test_timeout_functionality():
    """Test the timeout functionality of the Figma endpoint."""
    
    print("🧪 Testing Figma screen processing timeout functionality")
    print(f"📡 Target endpoint: {API_BASE_URL}{ENDPOINT}")
    print(f"⏱️  Expected timeout: 60 seconds")
    print(f"📋 Test data: {json.dumps(TEST_DATA, indent=2)}")
    print("-" * 60)
    
    start_time = time.time()
    
    try:
        # Create HTTP client with longer timeout than our API timeout
        # This ensures we can capture the API's timeout response
        async with httpx.AsyncClient(timeout=120.0) as client:
            print(f"🚀 [{time.time() - start_time:.2f}s] Sending request...")
            
            response = await client.post(
                f"{API_BASE_URL}{ENDPOINT}",
                json=TEST_DATA,
                headers=HEADERS
            )
            
            elapsed = time.time() - start_time
            print(f"📨 [{elapsed:.2f}s] Received response")
            print(f"📊 Status Code: {response.status_code}")
            print(f"⏱️  Total elapsed time: {elapsed:.2f} seconds")
            
            # Parse response
            try:
                response_data = response.json()
                print(f"📄 Response content:")
                print(json.dumps(response_data, indent=2))
            except Exception as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"📄 Raw response: {response.text}")
            
            # Analyze results
            print("\n" + "=" * 60)
            print("📊 ANALYSIS:")
            
            if response.status_code == 200:
                print("✅ SUCCESS: Request completed successfully")
                if elapsed < 60:
                    print(f"⚡ Fast completion: {elapsed:.2f}s (under 60s timeout)")
                else:
                    print(f"⚠️  Slow completion: {elapsed:.2f}s (over 60s - unexpected)")
                    
            elif response.status_code == 408:
                print("⏰ TIMEOUT: Request timed out as expected")
                if 58 <= elapsed <= 62:
                    print(f"✅ Timeout timing correct: {elapsed:.2f}s (within 60±2s)")
                else:
                    print(f"⚠️  Timeout timing unexpected: {elapsed:.2f}s (expected ~60s)")
                    
            elif response.status_code == 500:
                print("❌ ERROR: Server error occurred")
                
            else:
                print(f"❓ UNEXPECTED: Status code {response.status_code}")
            
            return {
                "success": True,
                "status_code": response.status_code,
                "elapsed_seconds": elapsed,
                "response_data": response_data if 'response_data' in locals() else None
            }
            
    except httpx.TimeoutException as e:
        elapsed = time.time() - start_time
        print(f"⏰ [{elapsed:.2f}s] HTTP Client timeout (this shouldn't happen with 120s timeout)")
        print(f"❌ Error: {e}")
        return {
            "success": False,
            "error": "HTTP client timeout",
            "elapsed_seconds": elapsed
        }
        
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ [{elapsed:.2f}s] Unexpected error: {e}")
        return {
            "success": False,
            "error": str(e),
            "elapsed_seconds": elapsed
        }

async def test_multiple_scenarios():
    """Test multiple scenarios to verify timeout behavior."""
    
    scenarios = [
        {
            "name": "Small request (should complete quickly)",
            "data": {
                **TEST_DATA,
                "screens": {
                    **TEST_DATA["screens"],
                    "selected_screen_ids": ["1:123"]  # Single screen
                }
            }
        },
        {
            "name": "Medium request (might timeout)",
            "data": {
                **TEST_DATA,
                "screens": {
                    **TEST_DATA["screens"],
                    "selected_screen_ids": ["1:123", "1:456", "1:789", "1:101", "1:102"]  # Multiple screens
                }
            }
        }
    ]
    
    results = []
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{'='*60}")
        print(f"🧪 SCENARIO {i}: {scenario['name']}")
        print(f"{'='*60}")
        
        # Update test data
        global TEST_DATA
        original_data = TEST_DATA.copy()
        TEST_DATA = scenario["data"]
        
        # Run test
        result = await test_timeout_functionality()
        result["scenario"] = scenario["name"]
        results.append(result)
        
        # Restore original data
        TEST_DATA = original_data
        
        # Wait between tests
        if i < len(scenarios):
            print(f"\n⏳ Waiting 5 seconds before next test...")
            await asyncio.sleep(5)
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 SUMMARY OF ALL TESTS:")
    print(f"{'='*60}")
    
    for i, result in enumerate(results, 1):
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"{i}. {result['scenario']}: {status} ({result['elapsed_seconds']:.2f}s)")
    
    return results

if __name__ == "__main__":
    print("🔧 Figma Timeout Test Script")
    print("=" * 60)
    print("⚠️  IMPORTANT: Make sure to update the following before running:")
    print("   1. API_BASE_URL - Set to your FastAPI server URL")
    print("   2. HEADERS['Authorization'] - Add valid JWT token")
    print("   3. TEST_DATA - Use actual project_id, figma_id, and screen_ids")
    print("=" * 60)
    
    # Check if user wants to proceed
    proceed = input("\n❓ Have you updated the configuration? (y/N): ").lower().strip()
    if proceed != 'y':
        print("❌ Please update the configuration first, then run the script again.")
        exit(1)
    
    # Run tests
    try:
        results = asyncio.run(test_multiple_scenarios())
        print(f"\n🎉 Testing completed! Check the results above.")
    except KeyboardInterrupt:
        print(f"\n⏹️  Testing interrupted by user.")
    except Exception as e:
        print(f"\n❌ Testing failed with error: {e}")
