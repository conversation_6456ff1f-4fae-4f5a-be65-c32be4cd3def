from typing import Dict, List, Optional, Any
from pydantic import BaseModel
from enum import Enum

class StepStatus(str, Enum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    SKIPPED = "skipped"

class StepInfo(BaseModel):
    step_name: str
    status: StepStatus
    node_id: Optional[int] = None
    node_type: Optional[str] = None
    completed_at: Optional[str] = None
    discussion_id: Optional[int] = None

class ProjectStepsStatus(BaseModel):
    project_id: int
    project_title: str
    steps: List[StepInfo]
    overall_progress: float  # Percentage of completed steps

# Step mapping configuration
STEP_NODE_MAPPING = {
    "document_upload": {
        "node_types": ["DocumentationRoot", "Documentation"],
        "description": "Document upload and processing"
    },
    "project_configuration": {
        "node_types": ["Project"],
        "description": "Project basic configuration"
    },
    "requirement_configuration": {
        "node_types": ["RequirementRoot"],
        "description": "Requirements gathering and configuration"
    },
    "architecture_requirement": {
        "node_types": ["ArchitecturalRequirement"],
        "description": "Architectural requirements definition"
    },
    "system_architecture": {
        "node_types": ["ArchitectureRoot", "SystemContext"],
        "description": "System architecture design"
    },
    "container": {
        "node_types": ["Container"],
        "description": "Container configuration and setup"
    },
    "code_generation": {
        "node_types": ["Component", "Architecture"],
        "description": "Code generation and implementation"
    }
}
