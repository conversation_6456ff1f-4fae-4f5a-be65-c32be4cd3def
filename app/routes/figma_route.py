# app/routes/figma_route.py
from llm_wrapper.core.llm_interface import LLMInterface
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Request, Body
from fastapi.responses import JSONResponse, StreamingResponse
from typing import Optional, List, Dict, Any, Union, Tuple
from pydantic import BaseModel
from app.utils.auth_utils import get_current_user as get_current_user_v1
from app.models.figma_model import FrameLink
from app.connection.establish_db_connection import get_node_db, NodeDB
from app.utils.figma_utils import (
    extract_file_key,   
    extract_frame_data,
    extract_all_node_data,
    fetch_frame_images,
    fetch_frame_image,
    get_frame_details,
    get_figma_access_token,
    get_figma_file_data_limited,
    figma_access_token,
)
from app.core.constants import FIGMA_BASE_PATH
from app.utils.kg_inspect.knowledge_reporter import Reporter

import requests
import io
import zipfile
import json
import re
import base64
import mimetypes
import time
import secrets
import glob
from app.models.uiux.figma_model import FigmaModel, UserModel, FigmaRequestModel
from app.models.project.project_model import ProjectModel
from datetime import datetime
from app.connection.tenant_middleware import get_tenant_id
from app.models.tenant.settings_model import TenantSettings
from app.classes.S3Handler import S3Handler
from app.core.Settings import settings
from app.models.uiux.figma_model import (
    FigmaSizesModel,
    ProcessingStatus,
    FigmaFrameModel,
)
import asyncio
from fastapi import BackgroundTasks
import httpx
from app.utils.figma_utils import (
    get_figma_file_data_limited_async,
    fetch_frame_images_async,
    process_frame
)
from app.core.websocket.client import WebSocketClient
from app.connection.establish_db_connection import get_mongo_db
from app.utils.datetime_utils import generate_timestamp

from app.discussions.figma.tools.extraction import Extraction
import uuid
from fastapi import Body
import os
import shutil

from pydantic import BaseModel
from app.models.figma_model import FigmaExtractionRequest, ImageTemplate, ExtractionTypes

class FigmaDocumentRequest(BaseModel):
    figma_link: str



from copy import copy, deepcopy

from app.core.constants import FIGMA_BASE_PATH as BASE_PATH

import traceback
from app.discussions.figma.tools.work_input_discovery_tool import WorkInputDiscovery
from pydantic import Field
from app.telemetry.logger_config import get_logger, setup_logging

from app.utils.file_utils.image_compressor import compress_image
from app.connection.establish_db_connection import get_node_db, NodeDB, get_mongo_db, MongoDBHandler
from app.core.constants import TASKS_COLLECTION_NAME
from app.utils.task_utils import get_codegen_url
from app.utils.project_utils import get_stage
from app.utils.code_generation_utils import get_codegeneration_path
from app.utils.datetime_utils import generate_timestamp

setup_logging()

_SHOW_NAME = "figma"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}},
)

# figma_access_token.set("*********************************************")


async def get_current_user(current_user=Depends(get_current_user_v1)):
    tenant_id = get_tenant_id()
    settings = await TenantSettings.get_settings(tenant_id)
    figma_api_key = next(
        (
            setting.value
            for setting in settings.integrations.figma
            if setting.name == "figma_api_key"
        ),
        "",
    )
    figma_access_token.set(figma_api_key)
    return current_user





async def process_figma_file(
    project_id: str, figma_link: FigmaRequestModel, is_new_file: bool = True
):
    """
    Common function to process Figma file for both add and update operations
    """
    tenant_id = get_tenant_id()
    file_key = extract_file_key(figma_link.url)
    file_name = f"{tenant_id}-{project_id}-{file_key}.json"

    if not file_key:
        raise HTTPException(status_code=400, detail="Invalid Figma link")

    s3_handler = S3Handler(tenant_id)

    # Check if file exists (only for new files)
    # if is_new_file and s3_handler.is_file(file_name):
    #     raise HTTPException(status_code=400, detail="Design already linked to this project")

    # Get and process Figma file data
    data, sizes = get_figma_file_data_limited(
        figma_link.url, get_figma_access_token(), mb_limit=2
    )
    frames = extract_frame_data(data)
    frame_ids = [frame["id"] for frame in frames]
    image_urls = fetch_frame_images(file_key, frame_ids)

    frames_with_images = [
        {**frame, "imageUrl": image_urls.get(frame["id"])} for frame in frames
    ]

    file_data = {
        "frames": frames_with_images,
        "fileKey": file_key,
        "document": data["document"],
        "sizes": sizes,
    }

    # Handle S3 storage
    if is_new_file:
        s3_handler.add_file(file_name, json.dumps(file_data))
    else:
        s3_handler.update_file(file_name, json.dumps(file_data))

    return file_data, sizes


async def list_figma_files_for_socket(
    ws_client,
    project_id,
    file_key,
    figma_logger,
    figma_id,
    frame_data_list,
    task_id = None,
    

):
    try:
        if figma_logger is None:
           
            figma_logger, _ = setup_figma_logger(project_id,tenant_id, figma_id,task_id)
        tenant_id = get_tenant_id()
        base_path = "/app/data"
        if os.environ.get("LOCAL_DEBUG"):
            base_path = "/tmp"
        figma_id = f"{tenant_id}-{project_id}-{file_key}"
        attachment_path = f"{base_path}/{tenant_id}/{project_id}/workspace/figma_json/{figma_id}"

        # Get frame data
        # frame_data_list = await get_figma_frame_data(figma_id=figma_id)
        
        # Handle error case
        if isinstance(frame_data_list, dict) and 'error' in frame_data_list:
            return JSONResponse(
                status_code=500,
                content={"message": f"Error getting frame data: {frame_data_list['error']}"}
            )
        
        # Build lookups for frame_id to image_url and frame_name, and get valid frame_ids
        frame_id_to_image_url = {item['frame_id']: item['image_url'] for item in frame_data_list}
        frame_id_to_frame_name = {item['frame_id']: item['frame_name'] for item in frame_data_list}
        valid_frame_ids = set(frame_id_to_image_url.keys())

        # List JSON files
        json_pattern = os.path.join(attachment_path, "*.json")
        json_files = glob.glob(json_pattern)

        files_with_info = []
        for file_path in json_files:
            filename = os.path.basename(file_path)
            if filename == "metadata.json":
                continue
            
            # Extract frame_id from filename: e.g., figma_{frame_id}.json
            if filename.startswith("figma_") and filename.endswith(".json"):
                frame_id = filename[len("figma_"):-len(".json")]
                
                # Only include files with frame_ids that exist in the frame data
                if frame_id in valid_frame_ids:
                    image_url = frame_id_to_image_url.get(frame_id)
                    frame_name = frame_id_to_frame_name.get(frame_id)
                    files_with_info.append({
                        'filename': filename,
                        'path': file_path,
                        'relative_path': os.path.relpath(file_path, base_path),
                        'frame_id': frame_id,
                        'image_url': image_url,
                        'frame_name': frame_name
                    })

        files_with_info.sort(key=lambda x: x['filename'])
        updated_files = {
                'files': files_with_info,
                'total_files': len(files_with_info),
                'directory_path': attachment_path,
                'valid_frame_ids': list(valid_frame_ids),  # Optional: for debugging
                'total_valid_frames': len(valid_frame_ids)  # Optional: for debugging
            }
        ws_client.send_message(
            "figma_json_files",
            {"figma_id": figma_id, "updated_files": updated_files}
        )
        list_id = "List_json"
        figma_logger.info(f"[{list_id}] send json file {updated_files}")
    except Exception as e:
        list_id = "List_json"
        figma_logger.error(f"[{list_id}] Error while sending json {str(e)}")
        ws_client.send_message(
            "figma_json_files",
            {"figma_id": figma_id, "error": str(e)}
        )
        

async def process_nodes_optimized_second_phase(
    all_nodes: List[Dict],
    attachment_path: str,
    project_id: str,
    tenant_id:str,
    file_key: str,
    image_urls: Dict,
    frame_children_map: Dict,
    ws_client,
    frame_data_list,
    frame_ids,
    figma_id: str,
    total_nodes: int,
    max_concurrent: int = 100,
    figma_logger = None,
    task_id = None
    
):
    """
    Optimized second phase processing with async semaphore
    Maintains exact same WebSocket message format and behavior
    """
    from datetime import datetime

    # Use the passed logger
    if figma_logger is None:
        # Fallback to creating a new logger if none provided
         # Extract project_id from figma_id
        figma_logger, _ = setup_figma_logger(project_id,tenant_id, figma_id,task_id)

    phase2_id = f"processing_json"
    figma_logger.info(f"[{phase2_id}] PHASE 2 STARTED: Processing {total_nodes} nodes with max_concurrent={max_concurrent}")
    figma_logger.info(f"[{phase2_id}] Parameters - attachment_path: {attachment_path}, file_key: {file_key}, figma_id: {figma_id}")

    # Create semaphore to limit concurrent operations
    semaphore = asyncio.Semaphore(max_concurrent)
    figma_logger.info(f"[{phase2_id}] Semaphore created with limit {max_concurrent}")

    # Counters for tracking
    json_processed_count = 0
    progress_lock = asyncio.Lock()
    batch_size = 10
    figma_logger.info(f"[{phase2_id}] Initialized counters - batch_size: {batch_size}")

    async def process_single_node(node: Dict, node_index: int) -> bool:
        """Process a single node with semaphore control"""
        nonlocal json_processed_count

        node_id = node.get('id', f'unknown_{node_index}')
        node_name = node.get('name', 'unknown')

        async with semaphore:
            try:
                figma_logger.info(f"[{phase2_id}] Node {node_index+1}/{total_nodes}: Processing node ID: {node_id}, Name: {node_name}")

                # Process each node normally (frames and non-frames)
                frame_model = await process_frame(node, file_key, image_urls)
                figma_logger.info(f"[{phase2_id}] Node {node_index+1}: Frame processed with status: {frame_model.status}")

                # Create frame data for local storage
                frame_data = create_frame_data(node, frame_model, frame_children_map)
                figma_logger.info(f"[{phase2_id}] Node {node_index+1}: Frame data created")
                
                # Store individual node file
                frame_file_path = os.path.join(attachment_path, f"figma_{node['id']}.json")
                await write_json_file(frame_file_path, frame_data)
                if node_id in frame_ids:
                    destination_base_path = "/app/data" if not os.environ.get("LOCAL_DEBUG") else "/tmp"
                    destination_path = f"{destination_base_path}/{tenant_id}/{project_id}/workspace/temp-attachments/"
                    os.makedirs(destination_path, exist_ok=True)
                    # Copy the file
                    shutil.copy2(frame_file_path, destination_path)
                    figma_logger.info(f"[{phase2_id}] Node {node_index+1}: Copying from {frame_file_path} to {destination_path} as it is frame node")

                figma_logger.info(f"[{phase2_id}] Node {node_index+1}: JSON file written to {frame_file_path}")

                # Update counter thread-safely
                async with progress_lock:
                    json_processed_count += 1
                    current_count = json_processed_count

                # Send WebSocket update every 10 processed nodes or on last iteration
                if (current_count % batch_size == 0) or (node_index == len(all_nodes) - 1):
                    json_progress_percentage = round((current_count / total_nodes) * 100, 1)
                    figma_logger.info(f"[{phase2_id}] Progress update: {current_count}/{total_nodes} ({json_progress_percentage}%)")

                    json_update = {
                        "type": "figma_update_json",
                        "processed_count": current_count,
                        "total_count": total_nodes,
                        "percentage": json_progress_percentage,
                        "time_updated": generate_timestamp(),
                    }

                    ws_client.send_message(
                        "figma_update_json",
                        {"figma_id": figma_id, "update_data": json_update}
                    )
                    figma_logger.info(f"[{phase2_id}] WebSocket progress update sent")
                    await list_figma_files_for_socket(ws_client,project_id,file_key,figma_logger,figma_id,frame_data_list,task_id)

                figma_logger.info(f"[{phase2_id}] Node {node_index+1}: Processing completed successfully")
                return True

            except Exception as e:
                figma_logger.error(f"[{phase2_id}] Node {node_index+1} ERROR: Error processing node {node_id}: {str(e)}")
                print(f"Error processing node {node['id']}: {str(e)}")

                # Store error frame data
                try:
                    figma_logger.info(f"[{phase2_id}] Node {node_index+1}: Attempting to store error frame data")
                    error_frame_data = create_error_frame_data(node, frame_children_map, str(e))
                    frame_file_path = os.path.join(attachment_path, f"figma_{node['id']}.json")
                    await write_json_file(frame_file_path, error_frame_data)
                    figma_logger.info(f"[{phase2_id}] Node {node_index+1}: Error frame data stored")

                    # Update counter thread-safely
                    async with progress_lock:
                        json_processed_count += 1
                        current_count = json_processed_count

                    # Send WebSocket update every 10 processed nodes or on last iteration
                    if (current_count % batch_size == 0) or (node_index == len(all_nodes) - 1):
                        json_progress_percentage = round((current_count / total_nodes) * 100, 1)
                        figma_logger.info(f"[{phase2_id}] Progress update (with error): {current_count}/{total_nodes} ({json_progress_percentage}%)")

                        json_update = {
                            "type": "figma_update_json",
                            "processed_count": current_count,
                            "total_count": total_nodes,
                            "percentage": json_progress_percentage,
                            "time_updated": generate_timestamp(),
                        }

                        ws_client.send_message(
                            "figma_update_json",
                            {"figma_id": figma_id, "update_data": json_update}
                        )
                        figma_logger.info(f"[{phase2_id}] WebSocket progress update sent (with error)")

                    figma_logger.info(f"[{phase2_id}] Node {node_index+1}: Error handling completed successfully")
                    return True

                except Exception as file_error:
                    figma_logger.error(f"[{phase2_id}] Node {node_index+1} CRITICAL ERROR: Error storing failed node to file: {str(file_error)}")
                    print(f"Error storing failed node to file: {str(file_error)}")
                    return False
    
    # Create tasks with node index for proper progress tracking
    figma_logger.info(f"[{phase2_id}] Creating {len(all_nodes)} concurrent tasks")
    tasks = [process_single_node(node, i) for i, node in enumerate(all_nodes)]
    figma_logger.info(f"[{phase2_id}] Tasks created, starting concurrent execution")

    # Execute all tasks concurrently
    start_time = datetime.now()
    results = await asyncio.gather(*tasks, return_exceptions=True)
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()

    # Count successful vs failed tasks
    successful_tasks = sum(1 for result in results if result is True)
    failed_tasks = len(results) - successful_tasks

    figma_logger.info(f"[{phase2_id}] PHASE 2 COMPLETED: Duration: {duration:.2f}s")
    figma_logger.info(f"[{phase2_id}] Results - Successful: {successful_tasks}, Failed: {failed_tasks}, Total processed: {json_processed_count}")

    return json_processed_count

async def background_process_figma_file(
    project_id: str,
    figma_link: FigmaRequestModel,
    tenant_id: str,
    figma_api_key: str,
    is_new_file: bool = True,
    figma_logger = None,
    task_id = None
):
    """Background task to process Figma file with frame status tracking - optimized single pass"""
    from datetime import datetime
    start_timer = time.time()
    # Use the passed logger or create a new one if not provided
    if figma_logger is None:
        file_key = extract_file_key(figma_link.url)
        figma_id = f"{tenant_id}-{project_id}-{file_key}"
        figma_logger, _ = setup_figma_logger(project_id,tenant_id, figma_id,task_id)

    task_id = f"background_task"
    figma_logger.info(f"[{task_id}] BACKGROUND TASK STARTED")
    figma_logger.info(f"[{task_id}] Input parameters - project_id: {project_id}, tenant_id: {tenant_id}, figma_url: {figma_link.url}, is_new_file: {is_new_file}")

    print("Background task started")
    file_logger = get_logger(__name__)

    # Initialize counters and data structures
    processed_frames = []
    completed_count = 0
    failed_count = 0
    total_frames = 0
    data = None
    has_errors = False

    try:
        # Step BG1: Setup figma access token
        figma_logger.info(f"[{task_id}] Step BG1: Setting figma access token")
        figma_access_token.set(figma_api_key)
        figma_logger.info(f"[{task_id}] Step BG1 SUCCESS: Figma access token set")

        # Step BG2: Extract file key
        figma_logger.info(f"[{task_id}] Step BG2: Extracting file key from URL: {figma_link.url}")
        file_key = extract_file_key(figma_link.url)
        figma_id = f"{tenant_id}-{project_id}-{file_key}"
        figma_logger.info(f"[{task_id}] Step BG2 SUCCESS: file_key = {file_key}, figma_id = {figma_id}")

        # Step BG3: Initialize LLM Interface
        figma_logger.info(f"[{task_id}] Step BG3: Initializing LLM Interface")
        llm_base_path = f"{BASE_PATH}/{project_id}/{tenant_id}/{figma_id}"
        figma_logger.info(f"[{task_id}] Step BG3a: LLM base path = {llm_base_path}")
        llm = LLMInterface(
            llm_base_path,
            instance_name=f"figma_discussion_{project_id}",
            user_id=tenant_id,
            project_id=project_id,
            agent_name="FigmaExtractionAgent"
        )
        figma_logger.info(f"[{task_id}] Step BG3 SUCCESS: LLM Interface initialized")

        # Step BG4: Initialize WorkInputDiscovery
        figma_logger.info(f"[{task_id}] Step BG4: Initializing WorkInputDiscovery")
        work_input_discovery = WorkInputDiscovery(
            callback_functions=None,
            base_path=llm_base_path,
            logger=file_logger,
            llm=llm
        )
        figma_logger.info(f"[{task_id}] Step BG4 SUCCESS: WorkInputDiscovery initialized")

        # Step BG5: Initialize WebSocket client
        figma_logger.info(f"[{task_id}] Step BG5: Initializing WebSocket client")
        ws_client_id = f"figma-{project_id}"
        figma_logger.info(f"[{task_id}] Step BG5a: WebSocket client ID = {ws_client_id}, URI = {settings.WEBSOCKET_URI}")
        ws_client = WebSocketClient(ws_client_id, uri=settings.WEBSOCKET_URI)
        connection_result = ws_client.connect()
        figma_logger.info(f"[{task_id}] Step BG5 SUCCESS: WebSocket client initialized, connection_result = {connection_result}")

        # Step BG6: Setup local storage paths
        figma_logger.info(f"[{task_id}] Step BG6: Setting up local storage paths")
        base_path = "/app/data" if not os.environ.get("LOCAL_DEBUG") else "/tmp"
        attachment_path = f"{base_path}/{tenant_id}/{project_id}/workspace/figma_json/{figma_id}"
        figma_logger.info(f"[{task_id}] Step BG6a: base_path = {base_path}, attachment_path = {attachment_path}")
        os.makedirs(attachment_path, exist_ok=True)
        figma_logger.info(f"[{task_id}] Step BG6 SUCCESS: Local storage paths created")

        # Step BG7: Create HTTP client and fetch Figma data
        figma_logger.info(f"[{task_id}] Step BG7: Creating HTTP client and fetching Figma data")
        async with httpx.AsyncClient() as client:
            figma_logger.info(f"[{task_id}] Step BG7a: HTTP client created")

            # Get Figma file data
            figma_logger.info(f"[{task_id}] Step BG7b: Fetching Figma file data with mb_limit=100")
            data, sizes = await get_figma_file_data_limited_async(
                client, figma_link.url, figma_api_key, mb_limit=100
            )
            figma_logger.info(f"[{task_id}] Step BG7b SUCCESS: Figma data fetched - size_kb: {sizes.get('size_kb', 'unknown')}, size_mb: {sizes.get('size_mb', 'unknown')}")

            # Step BG8: Extract frames and nodes
            figma_logger.info(f"[{task_id}] Step BG8: Extracting frames and nodes from data")
            frames = extract_frame_data(data)  # Only FRAME types for S3
            figma_logger.info(f"[{task_id}] Step BG8a: Extracted {len(frames)} frames")
            all_nodes = extract_all_node_data(data)  # All node types for local storage
            figma_logger.info(f"[{task_id}] Step BG8b: Extracted {len(all_nodes)} total nodes")

            # Use frames count for progress tracking
            total_frames = len(frames)
            total_nodes = len(all_nodes)
            figma_logger.info(f"[{task_id}] Step BG8 SUCCESS: total_frames = {total_frames}, total_nodes = {total_nodes}")

            # Step BG9: Build frame hierarchy map
            figma_logger.info(f"[{task_id}] Step BG9: Building frame hierarchy map")
            frame_children_map = build_frame_children_map(data)
            figma_logger.info(f"[{task_id}] Step BG9 SUCCESS: Frame hierarchy map built with {len(frame_children_map)} entries")

            # Step BG10: Determine processing status
            figma_logger.info(f"[{task_id}] Step BG10: Determining processing status")
            # status = (
            #     ProcessingStatus.FAILED
            #     if FigmaSizesModel(**sizes).model_dump()['size_kb'] >= 6000
            #     else ProcessingStatus.PROCESSING
            # )
            status = ProcessingStatus.PROCESSING
            figma_logger.info(f"[{task_id}] Step BG10 SUCCESS: Status set to {status}")

            # Step BG11: Initial MongoDB update
            figma_logger.info(f"[{task_id}] Step BG11: Performing initial MongoDB update")
            update_data = {
                "total_frames": total_frames,
                "completed_frames": 0,
                "failed_frames": 0,
                "sizes": FigmaSizesModel(**sizes).dict(),
                "time_updated": generate_timestamp(),
                "status": status,
            }
            await FigmaModel.update(figma_id, update_data)
            figma_logger.info(f"[{task_id}] Step BG11 SUCCESS: Initial MongoDB update completed")

            # Step BG12: Fetch frame images
            figma_logger.info(f"[{task_id}] Step BG12: Fetching frame images")
            frame_ids = [frame["id"] for frame in frames]
            figma_logger.info(f"[{task_id}] Step BG12a: Frame IDs to fetch images for: {len(frame_ids)} frames")
            image_urls = await fetch_frame_images_async(client, file_key, frame_ids)
            figma_logger.info(f"[{task_id}] Step BG12 SUCCESS: Frame images fetched - {len(image_urls)} image URLs retrieved")

            # Step BG13: Two-phase processing
            figma_logger.info(f"[{task_id}] Step BG13: Starting two-phase processing")
            figma_logger.info(f"[{task_id}] Step BG13: Phase 1 - Process frames for S3 storage and progress tracking")
            figma_logger.info(f"[{task_id}] Step BG13: Phase 2 - Process all nodes for local storage")

            # Process frames for S3 storage
            figma_logger.info(f"[{task_id}] Step BG13-P1: Starting Phase 1 - Processing {total_frames} frames for S3")
            frame_ids = []
            for i, frame in enumerate(frames):
                try:
                    figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}: Processing frame {i+1}/{total_frames} - ID: {frame.get('id', 'unknown')}, Name: {frame.get('name', 'unknown')}")

                    # Process frame
                    frame_model = await process_frame(frame, file_key, image_urls)
                    figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}a: Frame processed with status: {frame_model}")

                    # Create processed frame data for S3 aggregation
                    bounding_box = frame.get("absoluteBoundingBox", {})
                    if not bounding_box:
                        bounding_box = {
                            "x": 0,
                            "y": 0,
                            "width": 800,
                            "height": 600,
                        }
                        figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}b: Using default bounding box for frame")
                    else:
                        figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}b: Using frame bounding box: {bounding_box}")

                    processed_frame = {
                        "id": frame["id"],
                        "name": frame["name"],
                        "type": frame["type"],
                        "absoluteBoundingBox": bounding_box,
                        "imageUrl": (
                            frame_model.imageUrl
                            if frame_model.status == ProcessingStatus.COMPLETED
                            else None
                        ),
                        "status": frame_model.status,
                        "error_message": (
                            frame_model.error_message
                            if hasattr(frame_model, "error_message")
                            else None
                        ),
                        "time_updated": frame_model.time_updated,
                        "dimensions": {
                            "width": round(bounding_box.get("width", 800)),
                            "height": round(bounding_box.get("height", 600)),
                        },
                    }
                    frame_ids.append(frame["id"])
                    processed_frames.append(processed_frame)
                    figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}c: Frame data created and added to processed_frames")

                    # Update counters
                    if frame_model.status == ProcessingStatus.COMPLETED:
                        completed_count += 1
                        figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}d: Frame completed successfully, completed_count = {completed_count}")
                    elif frame_model.status == ProcessingStatus.FAILED:
                        failed_count += 1
                        has_errors = True
                        figma_logger.warning(f"[{task_id}] Step BG13-P1-F{i+1}d: Frame failed, failed_count = {failed_count}")

                    # Send progress update for each frame processed
                    # current_status = (
                    #     ProcessingStatus.PARTIALLY_COMPLETED if has_errors
                    #     else status
                    # )
                    current_status = status

                    progress_update = {
                        "total_frames": total_frames,
                        "completed_frames": completed_count,
                        "failed_frames": failed_count,
                        "time_updated": generate_timestamp(),
                        "sizes": FigmaSizesModel(**sizes).dict(),
                        "status": current_status,
                    }

                    figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}e: Sending WebSocket update - status: {current_status}")
                    # Send WebSocket update for frame processing
                    ws_client.send_message(
                        "figma_update",
                        {"figma_id": figma_id, "update_data": progress_update}
                    )

                    # Update MongoDB
                    await FigmaModel.update(figma_id, progress_update)
                    figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}f: MongoDB updated successfully")

                except Exception as e:
                    figma_logger.error(f"[{task_id}] Step BG13-P1-F{i+1} ERROR: Error processing frame {frame.get('id', 'unknown')}: {str(e)}")
                    print(f"Error processing frame {frame['id']}: {str(e)}")
                    failed_count += 1
                    has_errors = True
            # Step BG14: Transition to JSON processing phase
            figma_logger.info(f"[{task_id}] Step BG14: Phase 1 completed, transitioning to JSON processing phase")
            final_update = {
                "status": ProcessingStatus.PROCESSING_JSON,
                "error_message": None,
                "time_updated": generate_timestamp(),
            }
            await FigmaModel.update(figma_id, final_update)
            figma_logger.info(f"[{task_id}] Step BG14a: Sending PROCESSING_JSON status update via WebSocket")
            ws_client.send_message(
                "figma_update", {"figma_id": figma_id, "update_data": final_update}
            )
            figma_logger.info(f"[{task_id}] Step BG14 SUCCESS: Status updated to PROCESSING_JSON")
            # Step BG16: Store aggregated data in S3
            figma_logger.info(f"[{task_id}] Step BG16: Storing aggregated data in S3")
            file_data = {
                "frames": processed_frames,
                "fileKey": file_key,
                "document": data["document"],
                "sizes": sizes,
                "progress": {
                    "total": total_frames,
                    "completed": completed_count,
                    "failed": failed_count,
                },
            }
            figma_logger.info(f"[{task_id}] Step BG16a: File data structure created with {len(processed_frames)} frames")
            figma_logger.info(f"[{task_id}] Step BG18: Uploading file data to S3")
            # Step BG18: Upload to S3
            s3_handler = S3Handler(tenant_id)
            await s3_handler.update_file_async(
                f"{figma_id}.json", json.dumps(file_data)
            )
            figma_logger.info(f"[{task_id}] Step BG18 SUCCESS: File uploaded to S3 as {figma_id}.json")
            # Step BG15: Process all nodes for local storage
            figma_logger.info(f"[{task_id}] Step BG15: Starting Phase 2 - Processing all nodes for local storage")
            max_concurrent = min(100, max(10, len(all_nodes) // 10))
            figma_logger.info(f"[{task_id}] Step BG15a: Calculated max_concurrent = {max_concurrent} for {total_nodes} nodes")

            # Process all nodes for local storage (optimized)
            figma_logger.info(f"[{task_id}] Step BG15b: Starting optimized node processing")
            # frame_data_list = await get_figma_frame_data(figma_id=figma_id)
            # json_processed_count = await process_nodes_optimized_second_phase(
            #     all_nodes, attachment_path,project_id,tenant_id, file_key, image_urls, frame_children_map,
            #     ws_client,frame_data_list,frame_ids, figma_id, total_nodes, max_concurrent, figma_logger,task_id
            # )
            # figma_logger.info(f"[{task_id}] Step BG15 SUCCESS: Node processing completed - {json_processed_count} nodes processed")
            # Step BG17: Process work input discovery
            figma_logger.info(f"[{task_id}] Step BG17: Processing work input discovery")
            hashes, work_item = work_input_discovery.process_figma_json(file_data, figma_logger)
            figma_logger.info(f"[{task_id}] Step BG17 SUCCESS: Work input discovery completed - {len(hashes) if hashes else 0} hashes generated")

            
            

            # Step BG19: Store metadata file locally
            figma_logger.info(f"[{task_id}] Step BG19: Storing metadata file locally")
            metadata = create_metadata(file_key, sizes, total_nodes, len(all_nodes), 0)  # All nodes processed locally
            metadata_file_path = os.path.join(attachment_path, "metadata.json")
            await write_json_file(metadata_file_path, metadata)
            figma_logger.info(f"[{task_id}] Step BG19 SUCCESS: Metadata file stored at {metadata_file_path}")

            # Step BG20: Final status update
            figma_logger.info(f"[{task_id}] Step BG20: Performing final status update")
            final_status = (
                ProcessingStatus.COMPLETED
                if failed_count == 0
                else ProcessingStatus.PARTIALLY_COMPLETED
            )
            figma_logger.info(f"[{task_id}] Step BG20a: Final status determined as {final_status} (failed_count: {failed_count})")

            final_update = {
                "status": final_status,
                "error_message": (
                    f"{failed_count} frames failed to process" if failed_count > 0 else None
                ),
                "time_updated": generate_timestamp(),
            }
            figma_logger.info(f"[{task_id}] Step BG20b: Sending final WebSocket update")
            ws_client.send_message(
                "figma_update", {"figma_id": figma_id, "update_data": final_update}
            )

            figma_logger.info(f"[{task_id}] Step BG20c: Updating MongoDB with final status")
            await FigmaModel.update(figma_id, final_update)
            figma_logger.info(f"[{task_id}] Step BG20 SUCCESS: Final status update completed")

    except Exception as e:
        figma_logger.error(f"[{task_id}] BACKGROUND TASK FAILED: Exception occurred - {str(e)}")
        figma_logger.error(f"[{task_id}] Exception type: {type(e).__name__}")
        ws_client.send_message(
                "figma_error", {"figma_id": figma_id, "Error_message": f"Error Occured in Process retry once {type(e).__name__}"}
            )
        import traceback
        figma_logger.error(f"[{task_id}] Full traceback: {traceback.format_exc()}")
        print(f"Error in background task: {str(e)}")

        # Step BG-ERROR1: Store any successfully processed frames
        figma_logger.info(f"[{task_id}] Step BG-ERROR1: Attempting to store successfully processed frames")
        if processed_frames and data:
            figma_logger.info(f"[{task_id}] Step BG-ERROR1a: Found {len(processed_frames)} processed frames to store")
            file_data = {
                "frames": processed_frames,
                "fileKey": file_key,
                "document": data["document"],
                "sizes": sizes,
                "progress": {
                    "total": total_frames,
                    "completed": completed_count,
                    "failed": failed_count,
                },
            }

            try:
                figma_logger.info(f"[{task_id}] Step BG-ERROR1b: Uploading partial data to S3")
                s3_handler = S3Handler(tenant_id)
                await s3_handler.update_file_async(
                    f"{figma_id}.json", json.dumps(file_data)
                )
                figma_logger.info(f"[{task_id}] Step BG-ERROR1b SUCCESS: Partial data uploaded to S3")
            except Exception as s3_error:
                figma_logger.error(f"[{task_id}] Step BG-ERROR1b FAILED: S3 upload error - {str(s3_error)}")

            # Step BG-ERROR2: Update status based on what was completed
            figma_logger.info(f"[{task_id}] Step BG-ERROR2: Updating status based on completion (completed: {completed_count}, failed: {failed_count})")
            if completed_count > 0:
                error_status = ProcessingStatus.PARTIALLY_COMPLETED
                figma_logger.info(f"[{task_id}] Step BG-ERROR2a: Setting status to PARTIALLY_COMPLETED")
                error_update = {
                    "status": error_status,
                    "error_message": str(e),
                    "time_updated": generate_timestamp(),
                }
            else:
                error_status = ProcessingStatus.FAILED
                figma_logger.info(f"[{task_id}] Step BG-ERROR2a: Setting status to FAILED")
                error_update = {
                    "status": error_status,
                    "error_message": str(e),
                    "time_updated": generate_timestamp(),
                }

            try:
                figma_logger.info(f"[{task_id}] Step BG-ERROR2b: Updating MongoDB with error status: {str(e)}")
                await FigmaModel.update(figma_id, error_update)
                figma_logger.info(f"[{task_id}] Step BG-ERROR2b SUCCESS: MongoDB updated with error status")
            except Exception as db_error:
                figma_logger.error(f"[{task_id}] Step BG-ERROR2b FAILED: MongoDB update error - {str(db_error)}")

            try:
                figma_logger.info(f"[{task_id}] Step BG-ERROR2c: Sending error status via WebSocket")
                ws_client.send_message(
                    "figma_update", {"figma_id": figma_id, "update_data": error_update}
                )
                figma_logger.info(f"[{task_id}] Step BG-ERROR2c SUCCESS: Error status sent via WebSocket")
            except Exception as ws_error:
                figma_logger.error(f"[{task_id}] Step BG-ERROR2c FAILED: WebSocket send error - {str(ws_error)}")
        else:
            figma_logger.warning(f"[{task_id}] Step BG-ERROR1 SKIPPED: No processed frames or data to store")

    finally:
        
        figma_logger.info(f"[{task_id}] CLEANUP: Disconnecting WebSocket client")
        try:
            end_timer = time.time()
            time_taken = end_timer - start_timer
            figma_logger.info(f"[{task_id}] TOTAL TIME TAKEN: {time_taken:.2f} seconds total_node: {total_nodes} total_frame: {total_frames}")
            ws_client.disconnect()
            figma_logger.info(f"[{task_id}] CLEANUP SUCCESS: WebSocket client disconnected")
        except Exception as cleanup_error:
            figma_logger.error(f"[{task_id}] CLEANUP ERROR: WebSocket disconnect error - {str(cleanup_error)}")

        figma_logger.info(f"[{task_id}] BACKGROUND TASK COMPLETED")
def classify_exception_status_code(exception: Exception) -> int:
    """
    Classify exceptions and return appropriate HTTP status codes.
    
    Args:
        exception: The caught exception
        
    Returns:
        int: Appropriate HTTP status code
    """
    exception_type = type(exception).__name__
    exception_str = str(exception).lower()
    
    # Network/HTTP related errors (502 Bad Gateway)
    if isinstance(exception, (httpx.ConnectError, httpx.TimeoutException, httpx.NetworkError)):
        return 502
    
    # HTTP client errors from external APIs
    if isinstance(exception, httpx.HTTPStatusError):
        # For Figma API authentication errors, return 403
        if exception.response.status_code == 403 and 'figma.com' in str(exception.request.url):
            return 403
        # For other 4xx errors from external APIs that indicate client errors
        elif 400 <= exception.response.status_code < 500:
            return exception.response.status_code
        # For 5xx errors from external APIs, return 502 (bad gateway)
        else:
            return 502
    
    # Authentication/Authorization errors (401/403)
    if any(keyword in exception_str for keyword in ['unauthorized', 'forbidden', 'authentication', 'permission']):
        return 401 if 'unauthorized' in exception_str or 'authentication' in exception_str else 403
    
    # Validation/Bad Request errors (400)
    if (isinstance(exception, (ValueError, TypeError)) or 
        any(keyword in exception_str for keyword in ['invalid', 'bad request', 'validation', 'missing required'])):
        return 400
    
    # Resource not found (404)
    if any(keyword in exception_str for keyword in ['not found', 'does not exist', 'no such']):
        return 404
    
    # Conflict errors (409)
    if any(keyword in exception_str for keyword in ['already exists', 'conflict', 'duplicate']):
        return 409
    
    # Rate limiting (429)
    if any(keyword in exception_str for keyword in ['rate limit', 'too many requests', 'quota exceeded']):
        return 429
    
    # Database/Connection errors (503 Service Unavailable)
    if any(keyword in exception_str for keyword in ['database', 'connection', 'timeout', 'unavailable']):
        return 503
    
    # Default to 500 for unclassified exceptions
    return 500

def setup_figma_logger(project_id: str, tenant_id: str, figma_id: str, task_id: str = None):
    """Setup centralized figma logger with project-specific path"""
    import logging
    import os
    from datetime import datetime

    # Create logs directory structure: /tmp/kavia/[project_id]/logs/
    base_path = "/app/data" if not os.environ.get("LOCAL_DEBUG") else "/tmp"
    
    if task_id:
        tenant_id = "T0000"
        log_dir = f"{base_path}/{tenant_id}/{project_id}/logs/{task_id}/"
    else:
        log_dir = f"{base_path}/{tenant_id}/{project_id}/logs"
        
    os.makedirs(log_dir, exist_ok=True)

    # Setup dedicated logger
    logger_name = f'figma_{figma_id}'
    figma_logger = logging.getLogger(logger_name)
    figma_logger.setLevel(logging.INFO)

    # Remove existing handlers to avoid duplicates
    for handler in figma_logger.handlers[:]:
        figma_logger.removeHandler(handler)

    # Create log file path
    log_file = os.path.join(log_dir, f'{figma_id}.log')

    # Create file handler for .log file
    file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
    
    # Create simple formatter with only timestamp and message
    class SimpleFormatter(logging.Formatter):
        def format(self, record):
            # Create timestamp
            timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]
            
            # Format: TIMESTAMP - MESSAGE
            log_line = f"{timestamp} - {record.getMessage()}"
            
            return log_line

    # Set simple formatter
    formatter = SimpleFormatter()
    file_handler.setFormatter(formatter)
    
    # Add handler to logger
    figma_logger.addHandler(file_handler)
    figma_logger.propagate = False

    return figma_logger, log_file

############################################################################################################
@router.post("/add_figma_file_v2")
async def add_figma_file_v2(
    background_tasks: BackgroundTasks,
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
    task_id = None
):
    from datetime import datetime

    # Get tenant_id and file_key early for logger setup
    tenant_id = get_tenant_id()
    file_key = extract_file_key(figma_link.url)

    if not file_key:
        return JSONResponse(
            status_code=400, content={"message": "Invalid Figma link"}
        )

    figma_id = f"{tenant_id}-{project_id}-{file_key}"

    # Setup centralized logger
    figma_logger, log_file_path = setup_figma_logger(project_id,tenant_id, figma_id,task_id)

    request_id = f"Add_figma_file"
    figma_logger.info(f"[{request_id}] Starting add_figma_file_v2 endpoint")
    figma_logger.info(f"[{request_id}] Log file: {log_file_path}")
    figma_logger.info(f"[{request_id}] Input parameters - project_id: {project_id}, figma_url: {figma_link.url}, figma_name: {figma_link.name}")

    try:
        # Step 1: Get tenant ID (already done above)
        figma_logger.info(f"[{request_id}] Step 1: Getting tenant ID")
        figma_logger.info(f"[{request_id}] Step 1 SUCCESS: tenant_id = {tenant_id}")

        # Step 2: Extract file key from Figma URL (already done above)
        figma_logger.info(f"[{request_id}] Step 2: Extracting file key from URL: {figma_link.url}")
        figma_logger.info(f"[{request_id}] Step 2 SUCCESS: file_key = {file_key}")

        # Step 3: Create figma_id (already done above)
        figma_logger.info(f"[{request_id}] Step 3: Created figma_id = {figma_id}")

        # Step 4: Check if design already exists
        figma_logger.info(f"[{request_id}] Step 4: Checking if design already exists")
        existing_design = await FigmaModel.get_one(figma_id)
        if existing_design:
            figma_logger.warning(f"[{request_id}] Step 4 FAILED: Design already exists with id {figma_id}")
            return JSONResponse(
                status_code=400, content={"message": "Design already exists"}
            )
        figma_logger.info(f"[{request_id}] Step 4 SUCCESS: Design does not exist, proceeding")

        # Step 5: Create UserModel
        figma_logger.info(f"[{request_id}] Step 5: Creating UserModel from current_user")
        user_model = UserModel(
            username=current_user["sub"],
            name=current_user["custom:Name"],
            email=current_user["email"],
        )
        figma_logger.info(f"[{request_id}] Step 5 SUCCESS: UserModel created for user {user_model.username}")

        # Step 6: Get tenant settings
        figma_logger.info(f"[{request_id}] Step 6: Getting tenant settings")
        settings = await TenantSettings.get_settings(tenant_id)
        figma_logger.info(f"[{request_id}] Step 6 SUCCESS: Retrieved tenant settings")

        # Step 7: Extract Figma API key
        figma_logger.info(f"[{request_id}] Step 7: Extracting Figma API key from settings")
        figma_api_key = next(
            (
                setting.value
                for setting in settings.integrations.figma
                if setting.name == "figma_api_key"
            ),
            None,
        )

        if not figma_api_key:
            figma_logger.error(f"[{request_id}] Step 7 FAILED: Figma API key not configured in tenant settings")
            return JSONResponse(
                status_code=400, content={"message": "Figma API key not configured"}
            )
        figma_logger.info(f"[{request_id}] Step 7 SUCCESS: Figma API key found (length: {len(figma_api_key)})")

        # Step 8: Fetch Figma file data
        figma_logger.info(f"[{request_id}] Step 8: Fetching Figma file data with httpx client")
        async with httpx.AsyncClient() as client:
            figma_logger.info(f"[{request_id}] Step 8a: Created httpx AsyncClient")
            # Process Figma file data first to get total frames count
            data, sizes = await get_figma_file_data_limited_async(
                client, figma_link.url, figma_api_key, mb_limit=100
            )
            figma_logger.info(f"[{request_id}] Step 8b SUCCESS: Retrieved Figma data - size_kb: {sizes.get('size_kb', 'unknown')}, size_mb: {sizes.get('size_mb', 'unknown')}")
        # Step 9: Determine processing status
        figma_logger.info(f"[{request_id}] Step 9: Determining processing status")
        # if(sizes["size_kb"] < 6000):
        #     status = ProcessingStatus.PENDING
        # else:
        #     status = ProcessingStatus.FAILED
        status = ProcessingStatus.PENDING
        figma_logger.info(f"[{request_id}] Step 9 SUCCESS: Status set to {status}")

        # Step 10: Create figma_data dictionary
        figma_logger.info(f"[{request_id}] Step 10: Creating figma_data dictionary")
        figma_data = {
            "tenant_id": tenant_id,
            "project_id": project_id,
            "file_key": file_key,
            "id": figma_id,
            "name": figma_link.name,
            "url": figma_link.url,
            "added_by": user_model.dict(),
            "status": status,
            "total_frames": 0,
            "completed_frames": 0,
            "failed_frames": 0,
            "time_created": generate_timestamp(),
            "time_updated": generate_timestamp(),
            "sizes": FigmaSizesModel(**sizes).dict(),
        }
        figma_logger.info(f"[{request_id}] Step 10 SUCCESS: figma_data dictionary created")

        # Step 11: Add design to project
        figma_logger.info(f"[{request_id}] Step 11: Adding design to project {project_id}")
        success = await ProjectModel.add_design_id(project_id, figma_id)
        if not success:
            figma_logger.error(f"[{request_id}] Step 11 FAILED: Failed to add design to project")
            return JSONResponse(
                status_code=500, content={"message": "Failed to add design to project"}
            )
        figma_logger.info(f"[{request_id}] Step 11 SUCCESS: Design added to project")

        # Step 12: Create design document in database
        figma_logger.info(f"[{request_id}] Step 12: Creating design document in database")
        created_design = await FigmaModel.create(figma_data)
        if not created_design:
            figma_logger.error(f"[{request_id}] Step 12 FAILED: Failed to create design document")
            # Cleanup project if design creation fails
            figma_logger.info(f"[{request_id}] Step 12 CLEANUP: Removing design from project due to creation failure")
            await ProjectModel.remove_design_id(project_id, figma_id)
            return JSONResponse(
                status_code=500, content={"message": "Failed to create design"}
            )
        figma_logger.info(f"[{request_id}] Step 12 SUCCESS: Design document created in database")

        # Step 13: Setup background task
        figma_logger.info(f"[{request_id}] Step 13: Setting up background task")
        try:
            figma_logger.info(f"[{request_id}] Step 13a: Re-fetching tenant settings for background task")
            settings = await TenantSettings.get_settings(tenant_id)
            figma_api_key = next(
                (
                    setting.value
                    for setting in settings.integrations.figma
                    if setting.name == "figma_api_key"
                ),
            )
            figma_logger.info(f"[{request_id}] Step 13b: Re-extracted Figma API key for background task")

            if figma_api_key and status != ProcessingStatus.FAILED:
                figma_logger.info(f"[{request_id}] Step 13c: Preparing figma_link_dict for background task")
                figma_link_dict = {
                    "name": figma_link.name,
                    "url": figma_link.url
                }

                figma_logger.info(f"[{request_id}] Step 13d: Adding background task to process Figma file")
                # celery_task = Task.schedule_task(
                #     process_figma_in_celery,
                #     project_id=project_id,
                #     figma_link=figma_link_dict,
                #     figma_api_key=figma_api_key,
                #     is_new_file=True,
                #     tenant_id=get_tenant_id(),
                #     current_user=current_user.get("cognito:username"),
                # )
                background_tasks.add_task(
                    background_process_figma_file,
                    project_id,
                    figma_link,
                    tenant_id,
                    figma_api_key,
                    True,
                    figma_logger,  # Pass the logger instance
                    task_id
                )
                figma_logger.info(f"[{request_id}] Step 13e SUCCESS: Background task added successfully")
            else:
                figma_logger.warning(f"[{request_id}] Step 13 SKIPPED: Background task not added - api_key_exists: {bool(figma_api_key)}, status: {status}")
        except Exception as e:
            figma_logger.error(f"[{request_id}] Step 13 ERROR: Error setting up background task: {str(e)}")
            print(f"Error setting up background task: {str(e)}")  # Just log the error

        # Step 14: Return success response
        figma_logger.info(f"[{request_id}] Step 14: Returning success response")
        response_content = {
            "message": "Figma file processing started",
            "status": ProcessingStatus.PENDING,
            "id": figma_id
        }
        figma_logger.info(f"[{request_id}] Step 14 SUCCESS: Endpoint completed successfully - figma_id: {figma_id}")

        return JSONResponse(
            status_code=202,
            content=response_content,
        )

    except Exception as e:
        status_code = classify_exception_status_code(e)
        figma_logger.error(f"[{request_id}] ENDPOINT FAILED: Exception occurred - {str(e)}")
        figma_logger.error(f"[{request_id}] Exception type: {type(e).__name__}")
        figma_logger.error(f"[{request_id}] Status code determined: {status_code}")
        print(f"Error in add_figma_file_v2: {str(e)}")  # Debug print
        return JSONResponse(
            status_code=status_code,
            content={"message": f"An error occurred: {str(e)}"}
        )
@router.get("/figma_frame_datas/{project_id}")
async def get_figma_datas_by_project(
    project_id: str,
    current_user=Depends(get_current_user),
):
    """
    Retrieve all Figma data entries for a specific project from the figma_datas collection
    """
    try:
        # Get tenant_id
        tenant_id = get_tenant_id()
        
        # Get tenant settings to access MongoDB
        settings = await TenantSettings.get_settings(tenant_id)
        
        # Initialize MongoDB handler for figma_datas collection
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME, 
            collection_name="figma_datas"
        )
        
        # Find all figma_datas entries for the project
        figma_datas = await mongo_handler.find_many(
            {"project_id": project_id}
        )
        
        if not figma_datas:
            return JSONResponse(
                status_code=404,
                content={
                    "message": "No Figma data found for this project",
                    "project_id": project_id,
                    "data": []
                }
            )
        
        # Format the response data
        formatted_data = []
        for figma_data in figma_datas:
            # Convert ObjectId to string if present
            if '_id' in figma_data:
                figma_data['_id'] = str(figma_data['_id'])
            
            # Extract relevant information
            formatted_entry = {
                "figma_id": figma_data.get("figma_id"),
                "task_id": figma_data.get("task_id"),
                "figma_url": figma_data.get("figma_url"),
                "file_key": figma_data.get("file_key"),
                "screens_count": len(figma_data.get("screens", [])),
                "screens": figma_data.get("screens", []),
                "created_at": figma_data.get("created_at"),  # if you have this field
                "_id": figma_data.get("_id")
            }
            formatted_data.append(formatted_entry)
        
        return JSONResponse(
            status_code=200,
            content={
                "message": "Figma data retrieved successfully",
                "project_id": project_id,
                "total_entries": len(formatted_data),
                "data": formatted_data
            }
        )
        
    except Exception as e:
        status_code = classify_exception_status_code(e)
        print(f"Error in get_figma_datas_by_project: {str(e)}")
        return JSONResponse(
            status_code=status_code,
            content={"message": f"An error occurred: {str(e)}"}
        )

@router.put("/update_figma_file_v2")
async def update_figma_file_v2(
    background_tasks: BackgroundTasks,
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
):
    try:
        tenant_id = get_tenant_id()
        file_key = extract_file_key(figma_link.url)
        figma_id = f"{tenant_id}-{project_id}-{file_key}"

        # Check if design exists
        existing_design = await FigmaModel.get_one(figma_id)
        if not existing_design:
            return JSONResponse(
                status_code=404, content={"message": "Design not found"}
            )

        # Reset the design status and counters
        update_data = {
            "status": ProcessingStatus.PENDING,
            "total_frames": 0,
            "completed_frames": 0,
            "failed_frames": 0,
            "error_message": None,
            "time_updated": generate_timestamp(),
        }

        updated = await FigmaModel.update(figma_id, update_data)
        if not updated:
            return JSONResponse(
                status_code=500, content={"message": "Failed to update design status"}
            )

        # Get Figma API key and start background process
        try:
            settings = await TenantSettings.get_settings(tenant_id)
            figma_api_key = next(
                (
                    setting.value
                    for setting in settings.integrations.figma
                    if setting.name == "figma_api_key"
                ),
                "",
            )
            if figma_api_key:
                background_tasks.add_task(
                    background_process_figma_file,
                    project_id,
                    figma_link,
                    tenant_id,
                    figma_api_key,
                    False,
                )
        except:
            pass  # Continue even if background task setup fails

        return JSONResponse(
            status_code=202,
            content={
                "message": "Figma file update started",
                "status": ProcessingStatus.PENDING,
                "id": figma_id,
            },
        )

    except Exception as e:
        return JSONResponse(
            status_code=500, content={"message": f"An error occurred: {str(e)}"}
        )


@router.delete("/delete_design/{project_id}/{figma_id}")
async def delete_design(
    project_id: str, figma_id: str, current_user=Depends(get_current_user)
):
    """
    Delete a design including its S3 data and MongoDB records.

    Args:
        project_id (str): The project ID
        figma_id (str): The Figma design ID

    Returns:
        JSONResponse: Result of the deletion operation
    """
    deletion_results = {
        "s3_deleted": False,
        "mongodb_deleted": False,
        "project_ref_removed": False,
    }

    try:
        # First verify the design exists
        existing_design = await FigmaModel.get_one(figma_id)
        if not existing_design:
            return JSONResponse(
                status_code=404,
                content={
                    "message": "Design not found",
                    "id": figma_id,
                    "results": deletion_results,
                },
            )

        # Check if design belongs to project
        if existing_design.get("project_id") != project_id:
            return JSONResponse(
                status_code=400,
                content={
                    "message": "Design does not belong to this project",
                    "id": figma_id,
                    "results": deletion_results,
                },
            )

        # 1. Delete S3 data
        try:
            tenant_id = get_tenant_id()
            file_name = f"{figma_id}.json"
            s3_handler = S3Handler(tenant_id)
            if s3_handler.is_file(file_name):
                s3_handler.delete_file(file_name)
                deletion_results["s3_deleted"] = True
        except Exception as e:
            print(f"Error deleting S3 data: {str(e)}")

        # 2. Delete from figma_designs collection
        try:
            deleted = await FigmaModel.delete(figma_id)
            deletion_results["mongodb_deleted"] = bool(deleted)
        except Exception as e:
            print(f"Error deleting MongoDB data: {str(e)}")

        # 3. Remove reference from project
        try:
            removed = await ProjectModel.remove_design_id(project_id, figma_id)
            deletion_results["project_ref_removed"] = bool(removed)
        except Exception as e:
            print(f"Error removing project reference: {str(e)}")

        # Determine success based on critical operations
        if (
            deletion_results["mongodb_deleted"]
            and deletion_results["project_ref_removed"]
        ):
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Design deleted successfully",
                    "id": figma_id,
                    "results": deletion_results,
                },
            )
        else:
            # If some operations failed but others succeeded
            return JSONResponse(
                status_code=207,  # Multi-Status
                content={
                    "message": "Design deletion partially completed",
                    "id": figma_id,
                    "results": deletion_results,
                },
            )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "message": f"An error occurred during deletion: {str(e)}",
                "id": figma_id,
                "results": deletion_results,
            },
        )


@router.post("/reload_design/{project_id}/{figma_id}")
async def reload_design(
    background_tasks: BackgroundTasks,
    project_id: str,
    figma_id: str,
    current_user=Depends(get_current_user),
    task_id = None
):
    """Reload a Figma design by re-processing all frames"""
    try:
        # Verify design exists
        design = await FigmaModel.get_one(figma_id)
        if not design:
            return JSONResponse(
                status_code=404, content={"message": "Design not found"}
            )

        if design.get("project_id") != project_id:
            return JSONResponse(
                status_code=400,
                content={"message": "Design does not belong to this project"},
            )

        # Reset design status and counters
        update_data = {
            "status": ProcessingStatus.PENDING,
            "total_frames": 0,
            "completed_frames": 0,
            "failed_frames": 0,
            "error_message": None,
            "time_updated": generate_timestamp(),
        }

        updated = await FigmaModel.update(figma_id, update_data)
        if not updated:
            return JSONResponse(
                status_code=500, content={"message": "Failed to update design status"}
            )

        # Get Figma API key
        tenant_id = design.get("tenant_id")
        settings = await TenantSettings.get_settings(tenant_id)
        figma_api_key = next(
            (
                setting.value
                for setting in settings.integrations.figma
                if setting.name == "figma_api_key"
            ),
            None,
        )

        if not figma_api_key:
            return JSONResponse(
                status_code=400, content={"message": "Figma API key not configured"}
            )

        # Prepare figma link model
        figma_link = FigmaRequestModel(
            name=design.get("name", ""), url=design.get("url", "")
        )

        # Add the background task
        # This will be executed after the response is sent
        background_tasks.add_task(
            background_process_figma_file,
            project_id,
            figma_link,
            tenant_id,
            figma_api_key,
            False,
            task_id=task_id
        )

        # Return success response - background task will continue running
        return JSONResponse(
            status_code=202,
            content={
                "message": "Design reload started",
                "status": ProcessingStatus.PENDING,
                "id": figma_id,
            },
        )

    except Exception as e:
        # Update the design status to failed if we encounter an error
        try:
            await FigmaModel.update(
                figma_id,
                {
                    "status": ProcessingStatus.FAILED,
                    "error_message": str(e),
                    "time_updated": generate_timestamp(),
                },
            )
        except:
            pass  # Ignore errors in error handling

        return JSONResponse(
            status_code=500, content={"message": f"An error occurred: {str(e)}"}
        )


@router.get("/stream-status/{project_id}/{figma_id}")
async def stream_status(
    project_id: str,
    figma_id: str,
    stream: bool = Query(
        False,
        description="Stream updates until completion if True, else return current status",
    ),
    current_user=Depends(get_current_user),
):
    async def get_current_status():
        try:
            design = await FigmaModel.get_one(figma_id)
            if not design:
                return {"error": "Design not found", "status": ProcessingStatus.FAILED}

            return {
                "id": figma_id,
                "status": design.get("status", ProcessingStatus.PENDING),
                "total_frames": design.get("total_frames", 0),
                "completed_frames": design.get("completed_frames", 0),
                "failed_frames": design.get("failed_frames", 0),
                "error_message": design.get("error_message"),
                "time_updated": design.get("time_updated", generate_timestamp()),
            }
        except Exception as e:
            return {"error": str(e), "status": ProcessingStatus.FAILED}

    async def generate_status_events():
        prev_status = None
        retry_count = 0
        max_retries = 60  # 30 seconds with 0.5s sleep

        while retry_count < max_retries:
            try:
                status_data = await get_current_status()

                current_status = (
                    status_data.get("status"),
                    status_data.get("completed_frames"),
                    status_data.get("failed_frames"),
                )

                if current_status != prev_status:
                    yield f"data: {json.dumps(status_data)}\n\n"
                    prev_status = current_status

                if status_data.get("error") or status_data.get("status") in [
                    ProcessingStatus.COMPLETED,
                    ProcessingStatus.FAILED,
                ]:
                    break

                await asyncio.sleep(0.5)
                retry_count += 1

            except Exception as e:
                yield f"data: {json.dumps({'error': str(e), 'status': ProcessingStatus.FAILED})}\n\n"
                break

    if stream:
        return StreamingResponse(
            generate_status_events(), media_type="text/event-stream"
        )
    else:
        status_data = await get_current_status()
        return JSONResponse(content=status_data)


@router.post("/add_figma_file")
async def add_figma_file(
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        file_data, sizes = await process_figma_file(
            project_id, figma_link, is_new_file=True
        )

        # Add the design to the project
        user_model = UserModel(
            username=current_user["sub"],
            name=current_user["custom:Name"],
            email=current_user["email"],
        )
        figma_model = FigmaModel(
            id=project_id,
            name=figma_link.name,
            url=figma_link.url,
            added_by=user_model,
            sizes=FigmaSizesModel(**sizes),
        )

        # Get existing project if it exists
        existing_project = await ProjectModel.get_one(project_id)

        if existing_project:
            designs = existing_project.get("fields", {}).get("designs", [])
            designs.append(figma_model.model_dump())
            project_data = {"_id": project_id, "fields": {"designs": designs}}
        else:
            project_data = {
                "_id": project_id,
                "fields": {"designs": [figma_model.model_dump()]},
            }

        project_model = ProjectModel(**project_data)
        await project_model.upsert(project_data)

        return JSONResponse(content={"message": "Figma file added successfully"})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/delete_figma_file")
async def delete_figma_file(
    project_id: str,
    figma_design: FigmaRequestModel,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        tenant_id = get_tenant_id()
        figma_link = figma_design.url
        # Extract file key and generate filename
        file_key = extract_file_key(figma_link)
        file_name = f"{tenant_id}-{project_id}-{file_key}.json"

        # Delete file from S3
        s3_handler = S3Handler(tenant_id)
        if s3_handler.is_file(file_name):
            s3_handler.delete_file(file_name)

        # Get existing project
        existing_project = await ProjectModel.get_one(project_id)
        if not existing_project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Remove design from project's designs list
        designs = existing_project.get("fields", {}).get("designs", [])
        designs = [d for d in designs if d.get("url") != figma_link]

        # Update project with modified designs list
        project_data = {"_id": project_id, "fields": {"designs": designs}}
        project_model = ProjectModel(**project_data)
        await project_model.upsert(project_data)

        return JSONResponse(content={"message": "Figma file deleted successfully"})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/update_figma_file")
async def update_figma_file(
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        # Verify project exists
        existing_project = await ProjectModel.get_one(project_id)
        if not existing_project:
            raise HTTPException(status_code=404, detail="Project not found")

        file_data, sizes = await process_figma_file(
            project_id, figma_link, is_new_file=False
        )
        # Get existing designs
        designs = existing_project.get("fields", {}).get("designs", [])

        # Update the sizes for the matching design
        for design in designs:
            if design.get("url") == figma_link.url:
                figma_model = FigmaModel(
                    id=design.get("id"),
                    name=design.get("name"),
                    url=design.get("url"),
                    added_by=design.get("added_by"),
                    sizes=FigmaSizesModel(**sizes),
                    time_created=design.get("time_created"),
                    time_updated=generate_timestamp(),
                )
                design.update(figma_model.dict())
                break

        # Update project with modified designs
        project_data = {"_id": project_id, "fields": {"designs": designs}}
        project_model = ProjectModel(**project_data)
        await project_model.upsert(project_data)
        return JSONResponse(
            content={
                "message": "Figma file updated successfully",
                "file_data": file_data,
                "sizes": sizes,
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/get_figma_files")
async def get_figma_files(
    project_id: str,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        print(f"Project ID: {project_id}")
        designs, images  = await FigmaModel.get_by_project(project_id, include_images=True)
        return JSONResponse(content={"designs": designs, "images": images})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/get_figma_file_data")
async def get_figma_file_data(figma_id: str, current_user=Depends(get_current_user)):
    try:
        tenant_id = get_tenant_id()
        s3_handler = S3Handler(tenant_id)
        file_name = f"{figma_id}.json"
        data = s3_handler.get_file(file_name)
        data = json.loads(data.decode("utf-8"))
        return JSONResponse(content=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/file")
async def get_figma_file(
    project_id: str,
    figma_link: str,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        tenant_id = get_tenant_id()
        file_key = extract_file_key(figma_link)
        file_name = f"{tenant_id}-{project_id}-{file_key}.json"
        s3_handler = S3Handler(tenant_id)
        if not file_key:
            raise HTTPException(status_code=400, detail="Invalid Figma link")

        if not s3_handler.is_file(file_name):
            raise HTTPException(status_code=404, detail="File not found")

        # data = get_figma_file_data_limited(figma_link, get_figma_access_token(), kb_limit=500)
        data = s3_handler.get_file(file_name)
        data = json.loads(data.decode("utf-8"))

        return JSONResponse(
            content={
                "frames": data["frames"],
                "fileKey": file_key,
                "document": data["document"],
            }
        )
        # return JSONResponse(content={"frames": frames_with_images, "fileKey": file_key, "document": data["document"]})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/frame-image")
async def get_frame_image(
    figma_link: str, frame_id: str, current_user=Depends(get_current_user)
):
    file_key = extract_file_key(figma_link)
    image_url = fetch_frame_image(file_key, frame_id)
    if not image_url:
        raise HTTPException(status_code=404, detail="Frame image not found")
    return JSONResponse(content={"imageUrl": image_url})


@router.get("/download")
async def download_all_frames(figma_link: str, current_user=Depends(get_current_user)):
    file_key = extract_file_key(figma_link)
    if not file_key:
        raise HTTPException(status_code=400, detail="Invalid Figma link")

    print(file_key)
    zip_buffer = io.BytesIO()

    url = f"https://api.figma.com/v1/files/{file_key}"
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, headers=headers, timeout=300)
    response.raise_for_status()

    data = response.json()
    frames = extract_frame_data(data)

    with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
        for frame in frames:
            json_content = json.dumps(frame, indent=2)
            zip_file.writestr(f"{frame['name']}/{frame['name']}.json", json_content)

            image_url = fetch_frame_image(file_key, frame["id"])
            if image_url:
                image_response = requests.get(image_url, timeout=300)
                image_response.raise_for_status()
                zip_file.writestr(
                    f"{frame['name']}/{frame['name']}.png", image_response.content
                )

    zip_buffer.seek(0)
    return StreamingResponse(
        iter([zip_buffer.getvalue()]),
        media_type="application/zip",
        headers={
            "Content-Disposition": f"attachment; filename=figma_export_all_frames.zip"
        },
    )


@router.get("/frame-details")
async def get_frame_details_route(
    file_key: str, frame_id: str, current_user=Depends(get_current_user)
):
    frame_details = get_frame_details(file_key, frame_id)
    return JSONResponse(content=frame_details)


@router.get("/frame-preview")
async def get_frame_preview(
    figma_link: str, frame_id: str, current_user=Depends(get_current_user)
):
    file_key = extract_file_key(figma_link)
    url = f"https://api.figma.com/v1/images/{file_key}"
    params = {"ids": frame_id, "scale": 2, "format": "png"}
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, params=params, headers=headers, timeout=300)
    response.raise_for_status()
    preview_url = response.json()["images"].get(frame_id)

    if not preview_url:
        raise HTTPException(status_code=404, detail="Frame preview not found")
    return JSONResponse(content={"previewUrl": preview_url})


@router.get("/frame-thumbnail")
async def get_frame_thumbnail(
    figma_link: str, frame_id: str, current_user=Depends(get_current_user)
):
    file_key = extract_file_key(figma_link)
    url = f"https://api.figma.com/v1/images/{file_key}"
    params = {"ids": frame_id, "scale": 0.5, "format": "png"}
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, params=params, headers=headers, timeout=300)
    response.raise_for_status()
    thumbnail_url = response.json()["images"].get(frame_id)

    if not thumbnail_url:
        raise HTTPException(status_code=404, detail="Frame thumbnail not found")
    return JSONResponse(content={"thumbnailUrl": thumbnail_url})


# Update the link-frames endpoint to include image URLs
@router.post("/link-frames")
async def link_frames_to_design(
    frame_link: FrameLink,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    file_key = extract_file_key(frame_link.figma_link)
    if not file_key:
        raise HTTPException(status_code=400, detail="Invalid Figma link")

    design_node = await db.get_node_by_label_id(frame_link.design_id, "Design")
    if not design_node:
        raise HTTPException(status_code=404, detail="Design node not found")

    if not frame_link.frame_ids:
        updated_design = await db.update_node_by_id(
            frame_link.design_id, {"LinkedFigmaFrames": "[]"}
        )
        return JSONResponse(
            content={
                "message": "Frames linked successfully",
                "updated_design": updated_design,
            }
        )

    url = f"https://api.figma.com/v1/files/{file_key}/nodes"
    params = {"ids": ",".join(frame_link.frame_ids)}
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, params=params, headers=headers, timeout=300)
    response.raise_for_status()

    frames_data = response.json()["nodes"]

    # Fetch image URLs for the frames
    image_urls = fetch_frame_images(file_key, frame_link.frame_ids)

    linked_frames = design_node.get("properties", {}).get("LinkedFigmaFrames", [])
    if linked_frames:
        linked_frames = json.loads(linked_frames)
    if isinstance(linked_frames, str):
        linked_frames = json.loads(linked_frames)

    # Create a set of existing frame IDs for quick lookup
    existing_frame_ids = set(frame["id"] for frame in linked_frames)

    for frame_id, frame_data in frames_data.items():
        # Only add the frame if it doesn't already exist
        if frame_id not in existing_frame_ids:
            linked_frames.append(
                {
                    "id": frame_id,
                    "name": frame_data["document"]["name"],
                    "file_key": file_key,
                    "imageUrl": image_urls.get(frame_id),
                    "thumbnailUrl": f"https://api.figma.com/v1/images/{file_key}?ids={frame_id}&scale=0.5&format=png",
                }
            )
            existing_frame_ids.add(frame_id)  # Add the new frame ID to the set

    updated_design = await db.update_node_by_id(
        frame_link.design_id, {"LinkedFigmaFrames": json.dumps(linked_frames)}
    )

    return JSONResponse(
        content={
            "message": "Frames linked successfully",
            "updated_design": updated_design,
        }
    )


@router.post("/link-figma-components")
async def link_figma_components(
    frame_link: FrameLink,
    design_id: str,
    current_user=Depends(get_current_user),
):
    try:
        db = get_node_db()
        design_id = int(design_id)
        if not design_id:
            raise HTTPException(status_code=400, detail="Design ID is required")

        if not frame_link.figma_link:
            raise HTTPException(status_code=400, detail="Figma link is required")

        file_key = extract_file_key(frame_link.figma_link)
        if not file_key:
            raise HTTPException(status_code=400, detail="Invalid Figma link format")

        existing_design = await db.get_node_by_label_id(design_id, "Design")
        if not existing_design:
            raise HTTPException(status_code=404, detail="Design node not found")

        # Get existing components
        existing_components = []
        if existing_design.get("properties", {}).get("FigmaComponents"):
            try:
                existing_components = json.loads(
                    existing_design["properties"]["FigmaComponents"]
                )
                if not isinstance(existing_components, list):
                    existing_components = []
            except json.JSONDecodeError:
                existing_components = []

        if frame_link.unlink:
            # Remove the component if it exists
            existing_components = [
                comp for comp in existing_components if comp.get("file_key") != file_key
            ]
            message = "Figma component unlinked successfully"
        else:
            # Check if component already exists
            component_exists = False
            for component in existing_components:
                if component.get("file_key") == file_key:
                    component.update(
                        {
                            "figma_link": frame_link.figma_link,
                            "updated_at": datetime.utcnow().isoformat(),
                            "name": frame_link.name,  # Update name if changed
                        }
                    )
                    component_exists = True
                    break

            # If component doesn't exist, add it
            if not component_exists:
                new_component = {
                    "file_key": file_key,
                    "figma_link": frame_link.figma_link,
                    "name": frame_link.name,  # Store the name
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                    "created_by": {
                        "username": current_user.get("sub"),
                        "name": current_user.get("custom:Name"),
                        "email": current_user.get("email"),
                    },
                }
                existing_components.append(new_component)
            message = "Figma component linked successfully"

        # Update the design node
        design_node = await db.update_node_by_id(
            design_id, {"FigmaComponents": json.dumps(existing_components)}
        )

        if not design_node:
            raise HTTPException(
                status_code=500, detail="Failed to update design node with components"
            )

        return JSONResponse(
            content={
                "message": message,
                "updated_design": design_node,
                "components": existing_components,
            }
        )

    except HTTPException as e:
        raise e
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=400, detail="Invalid JSON format in existing components"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        )


@router.post("/check_design_in_db/{tenant_id}/{project_id}")
async def check_design_in_db(tenant_id: str, project_id: str, figmaDesigns: List[Dict]):
    try:
        deleted_info = []
        mongo_handler = get_mongo_db(
            db_name=(settings.MONGO_DB_NAME), collection_name="figma_designs"
        )
        for design in figmaDesigns:
            foundDesign = await mongo_handler.get_one(
                filter={"file_key": design["file_key"], "project_id": project_id},
                db=mongo_handler.db,
            )
            if foundDesign:
                deleted = False
            else:
                deleted = True
            deleted_info.append({design["file_key"]: deleted})
        return {"data": deleted_info}
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        )


class StartExtractionRequest(BaseModel):
    selected_design_id:str
    session_name:str = Field(default="Untitled")

@router.post("/start_discussion")
async def start_discussion(
    project_id: int,
    request: StartExtractionRequest,
    extraction_type: ExtractionTypes = ExtractionTypes.Figma,
    current_user=Depends(get_current_user),
):
    try:
        selected_design_id = request.selected_design_id
        session_name=request.session_name

        discussion_id = Extraction.start_discussion(project_id=project_id, 
                                                    selected_design_id=selected_design_id,
                                                    session_name=session_name,
                                                    extraction_type=extraction_type)
        
        return JSONResponse(content={"discussion_id": discussion_id})
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        )

@router.post("/figma_extraction")
async def figma_extraction(
    request: FigmaExtractionRequest,
    current_user=Depends(get_current_user)
):
    discussion_id = request.discussion_id
    user_request = request.user_request
    user_message = user_request.user_message
    file_attachments = user_request.file_attachments
    selected_frame = request.selected_frame
    # Use discussion_id as the task_id
    task_id = discussion_id
    
    # discussion = Extraction(discussion_id)
    # if file_attachments:
    #     discussion.set_file_attachments(file_attachments)
    # discussion.set_current_user(current_user=current_user.get("cognito:username"))
    # if selected_frame:
    #     discussion.set_selected_frame(selected_frame)
    # await discussion._initialize()
    # await discussion.load_figma_data()
    # end_response = {"stop": True}
    
    # # Create WebSocket session
    # ws_client = create_websocket_session(task_id, metadata={
    #     'task_type': 'figma_extraction',
    #     'discussion_id': discussion_id,
    #     'user_id': current_user.get("cognito:username")
    # })
    
    # # Send initial connection message
    # ws_client.send_message("connected", {
    #     "task_id": task_id,
    #     "status": "processing"
    # })
    
    # # This function will process the discussion and send via WebSocket
    # async def process_discussion():
    #     try:
    #         async for llm_response in discussion.main_discussion(user_message, stream=True,ws_client=ws_client):
    #             # Send to WebSocket
    #             ws_client.send_message("data", llm_response)
    #     except Exception as e:
    #         import traceback
    #         traceback.print_exc()
    #         error_msg = f"Error: {str(e)}"
    #         print(error_msg)
    #         # Send error to WebSocket
    #         ws_client.send_message("error", {"message": error_msg})
    #     finally:
    #         # Send end response to WebSocket
    #         ws_client.send_message("end", {"end":True})
    #         print(f"DEBUG: End Messsage : {end_response}")
    #         # Clean up WebSocket session
    #         # Delay cleanup by 5 seconds
    #         await asyncio.sleep(5)
    #         cleanup_websocket_session(task_id)
    
    # # Start the processing in a background task
    # asyncio.create_task(process_discussion())
    
    # Return the task_id for client to connect to WebSocket
    return {
        "task_id": task_id,
        "status": "processing in background"
    }
@router.get("/register_agent/{session_id}")
def register_agent(session_id: str):
    try:
        # Initialize WebSocket client and reporter
        ws_client = WebSocketClient(session_id, settings.WEBSOCKET_URI)
        reporter = Reporter(ws_client)
        reporter.initialize()
        
        return {
            "status": "success",
            "message": f"Agent successfully registered with session ID: {session_id}",
            "session_id": session_id
        }
        
    except ConnectionError as e:
        return {
            "status": "error",
            "message": f"Failed to establish WebSocket connection: {str(e)}",
            "session_id": session_id
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to register agent: {str(e)}",
            "session_id": session_id
        }

@router.get("/messages_history")
async def messages_history(discussion_id: str, current_user=Depends(get_current_user)):
    messages = Extraction.get_messages_history(discussion_id)
    return JSONResponse(content={"messages": messages})


@router.get("/past_discussions/{project_id}")
async def past_discussions(
    project_id: int,
    selected_design_id: str = None,
    current_user=Depends(get_current_user),
):
    print(f"Project ID: {project_id}")
    discussions = Extraction.get_past_discussions(project_id, selected_design_id)
    return JSONResponse(content={"discussions": discussions})


@router.get("/files/{discussion_id}")
async def files_content(discussion_id: str):
    """
    Returns a list of files generated for a specific Figma extraction discussion.

    Args:
        discussion_id (str): The unique identifier for the discussion

    Returns:
        JSONResponse: List of files with their paths and content
    """
    try:
        # Create the base directory path for this discussion
        base_dir = f"{BASE_PATH}/{discussion_id}"

        # Check if directory exists
        if not os.path.exists(base_dir):
            return JSONResponse(
                status_code=404,
                content={"message": f"No files found for discussion {discussion_id}"},
            )

        # Walk through the directory to find all files ignore logs folder and .log files
        file_list = []
        for root, dirs, files in os.walk(base_dir):
            for file in files:
                if file.endswith(".log") or file.startswith("logs"):
                    continue
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, base_dir)

                # Get file stats
                stats = os.stat(file_path)

                # Read file content if it's not too large
                content = ""
                if stats.st_size < 1024 * 1024:  # Limit to files < 1MB
                    try:
                        with open(file_path, "r") as f:
                            content = f.read()
                    except UnicodeDecodeError:
                        # Handle binary files
                        content = "(Binary file content not shown)"
                else:
                    content = f"(File too large: {stats.st_size / 1024:.2f} KB)"
                # if its a design_file.html, then make it index.html
                # if file == "design_file.html":
                #     file = "index.html"
                file_list.append(
                    {
                        "name": file,
                        "path": relative_path,
                        "full_path": file_path,
                        "size": stats.st_size,
                        "modified": stats.st_mtime,
                        "content": content,
                    }
                )

        # Sort files by name for consistency
        file_list.sort(key=lambda x: x["path"])

        return JSONResponse(
            status_code=200,
            content={
                "discussion_id": discussion_id,
                "base_path": base_dir,
                "files": file_list,
                "file_count": len(file_list),
            },
        )

    except Exception as e:
        return JSONResponse(
            status_code=500, content={"message": f"Error retrieving files: {str(e)}"}
        )


IMAGE_TEMPLATE = {
    "figma_ext_id" : "",
    "added_by" : {
        "username" : "",
        "name" : "",
        "email" : ""
    },
    "images" : [],
    "completed_frames" : 0,
    "failed_frames" : 0,
    "file_key" : "",
    "name" : "",
    "project_id" : "",
    "status" : "",
    "tenant_id" : "",
    "time_created" : "",
    "time_updated" : "",
    "total_frames" : 0,
    "path" : "",
    "error_message" : ""
}


@router.get("/get_ext_images/{project_id}/{image_ext_id}")
async def get_ext_images(project_id: str, image_ext_id: str ):
    image = get_mongo_db().db["figma_ext_images"].find_one({"project_id": project_id, "figma_ext_id": image_ext_id})
    if image:
        image.pop("_id")
        return JSONResponse(content={"image": image})
    else:
        return JSONResponse(content={"image": None})
    
class MergeChangesRequest(BaseModel):
    project_id: str
    discussion_id: str
    figma_ext_id: str
    type_of : str
@router.post("/merge_changes")
async def merge_changes(
    request: MergeChangesRequest,
    current_user=Depends(get_current_user),
    node_db: NodeDB = Depends(get_node_db)
):
    """
    Stores the file_path to the appropriate collection when a merge is performed.
    Also updates the project node in node_db with assets information.

    Args:
        request (MergeChangesRequest): The merge request data
        current_user: The authenticated user
        node_db: Database for node operations

    Returns:
        JSONResponse: Status of the merge operation
    """
    try:
        mongo_db = get_mongo_db().db
        if request.type_of == "image":
            collection = "figma_ext_images"
        elif request.type_of == "figma":
            collection = "figma_designs"
        else:
            return JSONResponse(
                status_code=400, 
                content={"message": "Type should be of image or figma"}
            )

        file_path = f"{BASE_PATH}/{request.discussion_id}/.assets"
        
        # Construct a query that uses the correct ID field based on type
        query = {"project_id": request.project_id}
        if request.type_of == "image":
            query["figma_ext_id"] = request.figma_ext_id
        else:  # figma type
            query["id"] = request.figma_ext_id
        
        # Find the existing record
        record = mongo_db[collection].find_one(query)
        
        if not record:
            return JSONResponse(
                status_code=404,
                content={"message": f"No record found for project {request.project_id} and ID {request.figma_ext_id}"}
            )
        
        # Create merged_by user info
        merged_by_info = {
            "username": current_user["sub"],
            "name": current_user["custom:Name"],
            "email": current_user["email"]
        }
        
        # Update data with the new merge information
        current_time = generate_timestamp()
        
        # If there are already merged files, add this one to the list
        if "merged_files" in record:
            # Add new file to the list of merged files
            update_data = {
                "time_updated": current_time,
                "$push": {
                    "merged_files": {
                        "extraction_path": file_path,
                        "file_path": file_path,
                        "discussion_id": request.discussion_id,
                        "merged_by": merged_by_info,
                        "merged_at": current_time
                    }
                }
            }
        else:
            # Create the initial merged_files array
            update_data = {
                "time_updated": current_time,
                "merged_files": [{
                    "extraction_path": file_path,
                    "file_path": file_path,
                    "discussion_id": request.discussion_id,
                    "merged_by": merged_by_info,
                    "merged_at": current_time
                }]
            }
        
        # Update the MongoDB record
        if "merged_files" in record:
            # Use update with $push operator when adding to existing array
            mongo_db[collection].update_one(
                query,
                {"$set": {"time_updated": current_time}, 
                 "$push": update_data["$push"]}
            )
        else:
            # Use regular update when creating the initial array
            mongo_db[collection].update_one(
                query,
                {"$set": update_data}
            )
        
        # Update project node in node_db with assets information
        import json
        
        # Try several ways to get the project node
        project_id = int(request.project_id)  # Ensure it's an integer
        
        # First try without specifying node type (more permissive)
        project_node = await node_db.get_node_by_id(project_id)
        
        # If that fails, try with a direct query that doesn't check is_active
        if not project_node:
            query = """
            MATCH (n) 
            WHERE ID(n) = $node_id 
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
            """
            query_result = await node_db.async_run(query, node_id=project_id)
            result = query_result.data()
            if result and len(result) > 0:
                project_node = result[0]
            else:
                return JSONResponse(
                    status_code=404,
                    content={"message": f"No project node found for project {request.project_id}"}
                )
        
        # Get current assets or initialize empty dict
        assets = {}
        if "assets" in project_node["properties"] and project_node["properties"]["assets"]:
            try:
                assets = json.loads(project_node["properties"]["assets"])
            except json.JSONDecodeError:
                # If assets data is not valid JSON, start with empty dict
                assets = {}
        
        # Update assets with the new design information
        design_id = request.figma_ext_id
        assets[design_id] = {"extracted_assets_path": file_path}
        
        # Update the project node with the new assets information
        project_properties = project_node["properties"].copy()
        project_properties["assets"] = json.dumps(assets)
        
        # Update the node in the database
        update_result = await node_db.update_node_by_id(project_id, project_properties)
        
        # If update fails, log it but don't fail the whole request
        if not update_result:
            print(f"Warning: Failed to update assets in project node {project_id}")
        
        return JSONResponse(
            status_code=200,
            content={
                "message": f"File {file_path} merged successfully",
                "project_id": request.project_id,
                "discussion_id": request.discussion_id,
                "figma_ext_id": request.figma_ext_id,
                "file_path": file_path,
                "extraction_path": file_path,
            }
        )
        
    except Exception as e:
        traceback.print_exc()
        print(f"Error in merge_changes: {str(e)}")
        return JSONResponse(
            status_code=500, 
            content={"message": f"An error occurred while merging changes: {str(e)}"}
        )
#################################################################################
@router.post("/create_ext_images")
async def add_ext_images(
    project_id: str,
    group_name: str,
    files: List[UploadFile] = File(...),
    current_user=Depends(get_current_user),
):
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        id = str(uuid.uuid4())[:8]    
        #create a fresh copy with deepcopy of IMAGE_TEMPLATE    
        tmp_image = deepcopy(IMAGE_TEMPLATE)
        
        tmp_image["added_by"]["username"] = current_user["sub"]
        tmp_image["added_by"]["name"] = current_user["custom:Name"]
        tmp_image["added_by"]["email"] = current_user["email"]
        tmp_image["project_id"] = project_id
        tmp_image["tenant_id"] = get_tenant_id()
        tmp_image["time_created"] = generate_timestamp()
        tmp_image["time_updated"] = generate_timestamp()
        
        tmp_image["figma_ext_id"] = id
        tmp_image["name"] = group_name
        tmp_image["status"] = "pending"
        tmp_image["total_frames"] = len(files)
        tmp_image["completed_frames"] = 0
        tmp_image["failed_frames"] = 0
        tenant_id = get_tenant_id()

        results = []
        path_extends = f"{BASE_PATH}/{tenant_id}/{project_id}/{id}/"
        os.makedirs(path_extends, exist_ok=True)
        tmp_image["images"] = []
        for file in files:
            try:
                # Save the uploaded file to the mongodb in base64url format
                file_content = await file.read()
                # Compress the image to 50%
                compressed_content, content_type, compression_ratio = compress_image(
                    file_content, target_ratio=0.5
                )
                
                base64_content = base64.b64encode(compressed_content).decode('utf-8')
                base64url = f"data:{file.content_type};base64,{base64_content}"
                image = ImageTemplate(
                    filename=file.filename, 
                    size=file.size,
                    file_type=file.content_type,
                    base64url=base64url,
                    error_message=""
                )
                tmp_image["images"].append(image.model_dump())

                tmp_image["completed_frames"] += 1
                tmp_image["time_updated"] = generate_timestamp()

                results.append({
                    "filename": file.filename,
                    "status": "success",
                    "base64url": base64url,
                    "compression_ratio": compression_ratio,
                    "original_size": len(file_content),
                    "compressed_size": len(compressed_content)
                })

            except Exception as e:
                tmp_image["failed_frames"] += 1
                tmp_image["time_updated"] = generate_timestamp()
                tmp_image["error_message"] = str(e)

                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": str(e)
                })
                
        tmp_image["path"] = path_extends
        tmp_image["status"] = "completed" if tmp_image["failed_frames"] == 0 else "failed"
        tmp_image["time_updated"] = generate_timestamp()

        
        mongo_db[collection].update_one(
            {"figma_ext_id": id},
            {"$set": tmp_image},
            upsert=True
        )

        return JSONResponse(
            status_code=200, 
            content={
                "message": f"Processed {len(files)} files", 
                "results": results
            }
        )

    except Exception as e:
        #traceback
        traceback.print_exc()
        print(f"Error in add_ext_images: {str(e)}")  # Fixed the debug print message
        return JSONResponse(
            status_code=500, content={"message": f"An error occurred: {str(e)}"}
        )
    
    

    
@router.post("/generate_assets")
async def generate_assets(
    project_id: int,
    selected_design_id: str,
    discussion_id: str,
    current_user=Depends(get_current_user)
):
    try:
        pass
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occurred: {str(e)}"
        )
    
@router.delete("/delete_ext_images/{figma_ext_id}")
async def delete_ext_images(
    figma_ext_id: str,
    current_user=Depends(get_current_user),
):
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        
        # First, fetch the document to get necessary info
        image_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        
        if not image_doc:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image with ID {figma_ext_id} not found"}
            )
        
        # Verify user has permission to delete
        # Option 1: Only creator can delete
        if image_doc["added_by"]["username"] != current_user["sub"]:
            return JSONResponse(
                status_code=403,
                content={"message": "You don't have permission to delete this resource"}
            )
        
        # Delete associated files from the filesystem
        # path_extends = image_doc["path"]
        # if os.path.exists(path_extends):
        #     try:
        #         shutil.rmtree(path_extends)
        #     except Exception as e:
        #         print(f"Warning: Could not delete directory {path_extends}: {str(e)}")
        
        # Delete from MongoDB
        delete_result = mongo_db[collection].delete_one({"figma_ext_id": figma_ext_id})
        
        if delete_result.deleted_count == 1:
            return JSONResponse(
                status_code=200,
                content={"message": f"Successfully deleted image group with ID {figma_ext_id}"}
            )
        else:
            return JSONResponse(
                status_code=500,
                content={"message": f"Failed to delete image group with ID {figma_ext_id}"}
            )
            
    except Exception as e:
        traceback.print_exc()
        print(f"Error in delete_ext_images: {str(e)}")
        return JSONResponse(
            status_code=500, 
            content={"message": f"An error occurred: {str(e)}"}
        )
    
@router.post("/add_more_images/{project_id}/{figma_ext_id}")
async def add_more_images(
    project_id: str,
    figma_ext_id: str,
    files: List[UploadFile] = File(...),
    current_user=Depends(get_current_user),
):
    """Add more images to an existing image group
    
    Args:
        project_id (str): Project identifier
        figma_ext_id (str): Existing image group identifier
        files (List[UploadFile]): List of files to add
        current_user: Current authenticated user
    """
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        
        # Get existing image document
        image_doc = mongo_db[collection].find_one({
            "figma_ext_id": figma_ext_id,
            "project_id": project_id
        })
        
        if not image_doc:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image group with ID {figma_ext_id} not found"}
            )
        
        # Verify user has permission
        if image_doc["added_by"]["username"] != current_user["sub"]:
            return JSONResponse(
                status_code=403,
                content={"message": "You don't have permission to modify this resource"}
            )
        
        results = []
        for file in files:
            try:
                # Convert file to base64url format
                file_content = await file.read()
                base64_content = base64.b64encode(file_content).decode('utf-8')
                base64url = f"data:{file.content_type};base64,{base64_content}"
                
                # Create image template
                image = ImageTemplate(
                    filename=file.filename,
                    size=file.size,
                    file_type=file.content_type,
                    base64url=base64url,
                    error_message=""
                )
                
                # Add to MongoDB array
                mongo_db[collection].update_one(
                    {"figma_ext_id": figma_ext_id},
                    {
                        "$push": {"images": image.model_dump()},
                        "$inc": {
                            "total_frames": 1,
                            "completed_frames": 1
                        },
                        "$set": {
                            "time_updated": generate_timestamp(),
                            "status": "completed"
                        }
                    }
                )

                results.append({
                    "filename": file.filename,
                    "status": "success",
                    "base64url": base64url
                })

            except Exception as e:
                # Update error count and status
                mongo_db[collection].update_one(
                    {"figma_ext_id": figma_ext_id},
                    {
                        "$inc": {
                            "total_frames": 1,
                            "failed_frames": 1
                        },
                        "$set": {
                            "time_updated": generate_timestamp(),
                            "status": "failed",
                            "error_message": str(e)
                        }
                    }
                )
                
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": str(e)
                })

        # Get updated document
        updated_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        if updated_doc:
            updated_doc.pop("_id")  # Remove MongoDB _id before sending response
        
        return JSONResponse(
            status_code=200,
            content={
                "message": f"Added {len(files)} files to image group {figma_ext_id}",
                "results": results,
                "updated_document": updated_doc
            }
        )

    except Exception as e:
        traceback.print_exc()
        print(f"Error in add_more_images: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred: {str(e)}"}
        )
@router.put("/rename_ext_image/{figma_ext_id}/{file_id}")
async def rename_ext_image(
    figma_ext_id: str,
    file_id: str,
    new_filename: str,
    current_user=Depends(get_current_user),
):
    """Rename a specific image in an image group
    
    Args:
        figma_ext_id (str): Image group identifier
        file_id (str): Image file identifier
        new_filename (str): New name for the file
        current_user: Current authenticated user
    """
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        
        # Get existing image document
        image_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        
        if not image_doc:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image group with ID {figma_ext_id} not found"}
            )
        
        # Verify user has permission
        if image_doc["added_by"]["username"] != current_user["sub"]:
            return JSONResponse(
                status_code=403,
                content={"message": "You don't have permission to modify this resource"}
            )
        
        # Find and update the specific image
        updated = mongo_db[collection].update_one(
            {
                "figma_ext_id": figma_ext_id,
                "images.file_id": file_id
            },
            {
                "$set": {
                    "images.$.filename": new_filename,
                    "time_updated": generate_timestamp()
                }
            }
        )
        
        if updated.modified_count == 0:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image with filename {file_id} not found in group"}
            )
            
        # Get updated document
        updated_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        if updated_doc:
            updated_doc.pop("_id")
        
        return JSONResponse(
            status_code=200,
            content={
                "message": f"Successfully renamed image {file_id} to {new_filename}",
                "updated_document": updated_doc
            }
        )

    except Exception as e:
        traceback.print_exc()
        print(f"Error in rename_ext_image: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred: {str(e)}"}
        )

@router.delete("/delete_ext_image/{figma_ext_id}/{file_id}")
async def delete_ext_image(
    figma_ext_id: str,
    file_id: str,
    current_user=Depends(get_current_user),
):
    """Delete a specific image from an image group
    
    Args:
        figma_ext_id (str): Image group identifier
        file_id (str): Image file identifier
        current_user: Current authenticated user
    """
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        
        # Get existing image document
        image_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        
        if not image_doc:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image group with ID {figma_ext_id} not found"}
            )
        
        # Verify user has permission
        if image_doc["added_by"]["username"] != current_user["sub"]:
            return JSONResponse(
                status_code=403,
                content={"message": "You don't have permission to modify this resource"}
            )
        
        # Remove the specific image and update counters
        updated = mongo_db[collection].update_one(
            {"figma_ext_id": figma_ext_id},
            {
                "$pull": {"images": {"file_id": file_id}},
                "$inc": {"total_frames": -1},
                "$set": {"time_updated": generate_timestamp()}
            }
        )
        
        if updated.modified_count == 0:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image with filename {file_id} not found in group"}
            )
        
        # Recalculate completed_frames and failed_frames
        updated_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        completed_frames = sum(1 for img in updated_doc["images"] if not img.get("error_message"))
        failed_frames = sum(1 for img in updated_doc["images"] if img.get("error_message"))
        
        # Update the counts
        mongo_db[collection].update_one(
            {"figma_ext_id": figma_ext_id},
            {
                "$set": {
                    "completed_frames": completed_frames,
                    "failed_frames": failed_frames,
                    "status": "completed" if failed_frames == 0 else "failed"
                }
            }
        )
        
        # Get final updated document
        final_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        if final_doc:
            final_doc.pop("_id")
        
        return JSONResponse(
            status_code=200,
            content={
                "message": f"Successfully deleted image {file_id}",
                "updated_document": final_doc
            }
        )

    except Exception as e:
        traceback.print_exc()
        print(f"Error in delete_ext_image: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred: {str(e)}"}
        )    
@router.get("/download_figma_code/{discussion_id}")
async def download_code(discussion_id: str):
    """
    Download all Code for a specific figma extraction as a zip file.
    Provides a browser download experience similar to downloading images from Google.
    
    Args:
        discussion_id: discussion ID of the figma extraction
        
    Returns:
        Streaming response with zip file
    """
    try:
        
        code_dir = f"{FIGMA_BASE_PATH}/{discussion_id}/.assets"
        
        # Check if directory exists
        if not os.path.exists(code_dir):
            raise HTTPException(status_code=404, detail=f"No codes file found for {discussion_id}")
        
        # Create timestamp for unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"figma_extraction_{discussion_id}_{timestamp}.zip"
        
        # Create zip file in memory
        zip_io = io.BytesIO()
        with zipfile.ZipFile(zip_io, mode='w', compression=zipfile.ZIP_DEFLATED) as zipf:
            # Walk through the directory and add all files
            for root, _, files in os.walk(code_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    # Add file to zip with relative path
                    arcname = os.path.relpath(file_path, code_dir)
                    zipf.write(file_path, arcname)
        
        # Reset the pointer to the beginning of the BytesIO object
        zip_io.seek(0)
        
        # Return streaming response with appropriate headers for download
        headers = {
            'Content-Disposition': f'attachment; filename="{filename}"',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
        
        return StreamingResponse(
            zip_io, 
            media_type="application/zip",
            headers=headers
        )
        
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        import traceback
        print(f"Error creating Code's zip file: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to create Code's File archive: {str(e)}")


async def background_process_figma_file_CGA(
    project_id: str,
    figma_link: str,
    task_id: str,
    tenant_id: str,
    figma_api_key: str,
    is_new_file: bool = True,
):
    """Background task to process Figma file with frame status tracking"""
    print("Background task started")
    file_logger = get_logger(__name__)
    completed_count = 0
    failed_count = 0
    total_frames = 0
    data = None

    # Setup local storage path
    base_path = "/app/data"
    if os.environ.get("LOCAL_DEBUG"):
        base_path = "/tmp"
    
    attachment_path = f"{base_path}/{tenant_id}/{project_id}/workspace/figma_json"
    
    # Create directory if it doesn't exist
    os.makedirs(attachment_path, exist_ok=True)
    ws_client = WebSocketClient(f"{task_id}", uri=settings.WEBSOCKET_URI)
    ws_client.connect()

    try:
        figma_access_token.set(figma_api_key)
        file_key = extract_file_key(figma_link)
        figma_id = f"{tenant_id}-{project_id}-{task_id}-{file_key}"

        # Use async client for HTTP requests
        async with httpx.AsyncClient() as client:
            # Process Figma file data first to get total frames count
            data, sizes = await get_figma_file_data_limited_async(
                client, figma_link, figma_api_key, mb_limit=100
            )
            frames = extract_all_node_data(data)
            total_frames = len(frames)

            # Build frame hierarchy map to find children relationships
            frame_children_map = build_frame_children_map(data)

            # Update FigmaModel with initial frame count and status
            update_data = {
                "total_frames": total_frames,
                "completed_frames": 0,
                "failed_frames": 0,
                "sizes": FigmaSizesModel(**sizes).dict(),
                "time_updated": generate_timestamp(),
            }
            await FigmaModel.update_CGA(figma_id, update_data)

            # Process frames and store each individually
            frame_ids = [frame["id"] for frame in frames]
            image_urls = await fetch_frame_images_async(client, file_key, frame_ids)
            has_errors = False

            # Determine initial status based on file size
            status = (
                ProcessingStatus.FAILED 
                if FigmaSizesModel(**sizes).model_dump()['size_kb'] >= 6000 
                else ProcessingStatus.PROCESSING
            )

            # Process each frame
            for i, frame in enumerate(frames):
                try:
                    frame_model = await process_frame(frame, file_key, image_urls)
                    frame_data = create_frame_data(frame, frame_model, frame_children_map)
                    
                    # Store individual frame locally
                    frame_file_path = os.path.join(attachment_path, f"figma_{frame['id']}.json")
                    await write_json_file(frame_file_path, frame_data)

                    # Update counts
                    if frame_model.status == ProcessingStatus.COMPLETED:
                        completed_count += 1
                    elif frame_model.status == ProcessingStatus.FAILED:
                        failed_count += 1
                        has_errors = True

                    # Send WebSocket update every 10 frames or at the end
                    if (i + 1) % 10 == 0 or i == len(frames) - 1:
                        update_data = {
                            "total_frames": total_frames,
                            "completed_frames": completed_count,
                            "failed_frames": failed_count,
                            "time_updated": generate_timestamp(),
                            "sizes": FigmaSizesModel(**sizes).dict(),
                            "status": ProcessingStatus.PARTIALLY_COMPLETED if has_errors else status,
                            "progress": round(((i + 1) / total_frames) * 100, 2),
                            "current_frame": i + 1,
                            "message": f"Processed {i + 1}/{total_frames} frames" + (f" ({failed_count} failed)" if failed_count > 0 else "")
                        }
                        
                        # Send WebSocket update
                        ws_client.send_message(
                            "figma_update",
                            {"figma_id": figma_id, "update_data": update_data}
                        )
                except Exception as e:
                    print(f"Error processing frame {frame['id']}: {str(e)}")
                    failed_count += 1
                    has_errors = True

                    # Store error frame data
                    error_frame_data = create_error_frame_data(frame, frame_children_map, str(e))
                    
                    try:
                        frame_file_path = os.path.join(attachment_path, f"figma_{frame['id']}.json")
                        await write_json_file(frame_file_path, error_frame_data)
                    except Exception as file_error:
                        print(f"Error storing failed frame to file: {str(file_error)}")
                        if (i + 1) % 10 == 0 or i == len(frames) - 1:
                            update_data = {
                                "total_frames": total_frames,
                                "completed_frames": completed_count,
                                "failed_frames": failed_count,
                                "time_updated": generate_timestamp(),
                                "sizes": FigmaSizesModel(**sizes).dict(),
                                "status": ProcessingStatus.PARTIALLY_COMPLETED if has_errors else status,
                                "progress": round(((i + 1) / total_frames) * 100, 2),
                                "current_frame": i + 1,
                                "message": f"Processed {i + 1}/{total_frames} frames ({failed_count} failed)"
                            }
                            
                            # Send WebSocket update
                            ws_client.send_message(
                                "figma_update",
                                {"figma_id": figma_id, "update_data": update_data}
                            )

            # Store metadata file
            metadata = create_metadata(file_key, sizes, total_frames, completed_count, failed_count)
            metadata_file_path = os.path.join(attachment_path, "metadata.json")
            await write_json_file(metadata_file_path, metadata)

        # Final status update
        final_status = (
            ProcessingStatus.COMPLETED
            if failed_count == 0
            else ProcessingStatus.PARTIALLY_COMPLETED
        )
        update_data = {
                        "total_frames": total_frames,
                        "completed_frames": completed_count,
                        "failed_frames": failed_count,
                        "time_updated": generate_timestamp(),
                        "sizes": FigmaSizesModel(**sizes).dict(),
                        "status": ProcessingStatus.PARTIALLY_COMPLETED if has_errors else status,
                    }
                    
        await FigmaModel.update_CGA(figma_id, update_data)
        
        final_update = {
            "status": final_status,
            "error_message": (
                f"{failed_count} frames failed to process" if failed_count > 0 else None
            ),
            "time_updated": generate_timestamp(),
        }
        
        await FigmaModel.update_CGA(figma_id, final_update)
        
        # Success message
        success_msg = f"Figma file processing completed successfully! Processed {completed_count}/{total_frames} frames."
        if failed_count > 0:
            success_msg += f" {failed_count} frames failed to process."
        
        print(success_msg)
        return {"status": "success", "message": success_msg}

    except Exception as e:
        print(f"Error in background task: {str(e)}")
        
        # Store error metadata if possible
        if 'figma_id' in locals():
            try:
                error_metadata = create_error_metadata(
                    locals().get('file_key'),
                    locals().get('sizes', {}),
                    total_frames,
                    completed_count,
                    failed_count,
                    str(e)
                )
                
                metadata_file_path = os.path.join(attachment_path, "metadata.json")
                await write_json_file(metadata_file_path, error_metadata)
            except Exception as file_error:
                print(f"Error storing error metadata to file: {str(file_error)}")

            # Update status based on progress
            error_status = (
                ProcessingStatus.PARTIALLY_COMPLETED 
                if completed_count > 0 
                else ProcessingStatus.FAILED
            )
            
            error_update = {
                "status": error_status,
                "error_message": str(e),
                "time_updated": generate_timestamp(),
            }
            
            await FigmaModel.update_CGA(figma_id, error_update)
        
        return {"status": "error", "message": f"Processing failed: {str(e)}"}


def build_frame_children_map(data: Dict[str, Any]) -> Dict[str, list]:
    """Build a map of all node children relationships"""
    frame_children_map = {}
    
    def build_children_map(node: Dict[str, Any], parent_id: str = None):
        current_node_id = node["id"]
        frame_children_map[current_node_id] = []
        
        if "children" in node:
            for child in node["children"]:
                frame_children_map[current_node_id].append(child["id"])
                build_children_map(child, current_node_id)
    
    if data.get("document"):
        build_children_map(data["document"])
    
    return frame_children_map


def create_frame_data(frame: Dict[str, Any], frame_model, frame_children_map: Dict[str, list]) -> Dict[str, Any]:
    """Create comprehensive frame data with all available details"""
    bounding_box = frame.get("absoluteBoundingBox", {})
    if not bounding_box:
        bounding_box = {"x": 0, "y": 0, "width": 800, "height": 600}

    children_frame_ids = frame_children_map.get(frame["id"], [])

    frame_data = {
        "id": frame["id"],
        "name": frame["name"],
        "type": frame["type"],
        "absoluteBoundingBox": bounding_box,
        "children": children_frame_ids,
        "imageUrl": (
            frame_model.imageUrl
            if frame_model.status == ProcessingStatus.COMPLETED
            else None
        ),
        "status": frame_model.status,
        "error_message": (
            frame_model.error_message
            if hasattr(frame_model, "error_message")
            else None
        ),
        "time_updated": frame_model.time_updated,
        "dimensions": {
            "width": round(bounding_box.get("width", 800)),
            "height": round(bounding_box.get("height", 600)),
        },
        # Include all other available frame details
        **{k: v for k, v in {
            "visible": frame.get("visible", True),
            "locked": frame.get("locked", False),
            "backgroundColor": frame.get("backgroundColor", ""),
            "exportSettings": frame.get("exportSettings", []),
            "blendMode": frame.get("blendMode", ""),
            "preserveRatio": frame.get("preserveRatio", ""),
            "layoutAlign": frame.get("layoutAlign", ""),
            "layoutGrow": frame.get("layoutGrow", ""),
            "layoutSizingHorizontal": frame.get("layoutSizingHorizontal", ""),
            "layoutSizingVertical": frame.get("layoutSizingVertical", ""),
            "effects": frame.get("effects", []),
            "opacity": frame.get("opacity", ""),
            "relativeTransform": frame.get("relativeTransform", ""),
            "size": frame.get("size", ""),
            "constraintsHorizontal": frame.get("constraintsHorizontal", ""),
            "constraintsVertical": frame.get("constraintsVertical", ""),
            "fills": frame.get("fills", []),
            "strokes": frame.get("strokes", []),
            "strokeWeight": frame.get("strokeWeight", ""),
            "strokeAlign": frame.get("strokeAlign", ""),
            "strokeCap": frame.get("strokeCap", ""),
            "strokeJoin": frame.get("strokeJoin", ""),
            "strokeDashes": frame.get("strokeDashes", ""),
            "strokeMiterLimit": frame.get("strokeMiterLimit", ""),
            "cornerRadius": frame.get("cornerRadius", ""),
            "rectangleCornerRadii": frame.get("rectangleCornerRadii", ""),
            "clipsContent": frame.get("clipsContent", ""),
            "background": frame.get("background", ""),
            "layoutMode": frame.get("layoutMode", ""),
            "itemSpacing": frame.get("itemSpacing", ""),
            "paddingLeft": frame.get("paddingLeft", ""),
            "paddingTop": frame.get("paddingTop", ""),
            "paddingRight": frame.get("paddingRight", ""),
            "paddingBottom": frame.get("paddingBottom", ""),
            "horizontalPadding": frame.get("horizontalPadding", ""),
            "verticalPadding": frame.get("verticalPadding", ""),
            "itemReverseZIndex": frame.get("itemReverseZIndex", ""),
            "strokesIncludedInLayout": frame.get("strokesIncludedInLayout", ""),
            "primaryAxisAlignItems": frame.get("primaryAxisAlignItems", ""),
            "counterAxisAlignItems": frame.get("counterAxisAlignItems", ""),
            "primaryAxisSizingMode": frame.get("primaryAxisSizingMode", ""),
            "counterAxisSizingMode": frame.get("counterAxisSizingMode", ""),
            "layoutWrap": frame.get("layoutWrap", ""),
            "layoutGrids": frame.get("layoutGrids", []),
            "overflowDirection": frame.get("overflowDirection", ""),
            "numberOfFixedChildren": frame.get("numberOfFixedChildren", ""),
            "overlayPositionType": frame.get("overlayPositionType", ""),
            "overlayBackground": frame.get("overlayBackground", ""),
            "overlayBackgroundInteraction": frame.get("overlayBackgroundInteraction", ""),
            "prototypeStartNodeID": frame.get("prototypeStartNodeID", ""),
            "prototypeDevice": frame.get("prototypeDevice", ""),
            "flowStartingPoints": frame.get("flowStartingPoints", []),
            "reactions": frame.get("reactions", []),
            "transitionNodeID": frame.get("transitionNodeID", ""),
            "transitionDuration": frame.get("transitionDuration", ""),
            "transitionEasing": frame.get("transitionEasing", ""),
            "rotation": frame.get("rotation", ""),
            "componentPropertyReferences": frame.get("componentPropertyReferences", ""),
            "boundVariables": frame.get("boundVariables", ""),
            "componentPropertyDefinitions": frame.get("componentPropertyDefinitions", ""),
            "scrollBehavior": frame.get("scrollBehavior", ""),
            "guides": frame.get("guides", []),
            "selection": frame.get("selection", ""),
            "prototypeBackgroundColor": frame.get("prototypeBackgroundColor", ""),
        }.items() if v not in [None, "", []]}
    }

    return {k: v for k, v in frame_data.items() if v is not None}


def create_error_frame_data(frame: Dict[str, Any], frame_children_map: Dict[str, list], error_msg: str) -> Dict[str, Any]:
    """Create error frame data with available details"""
    children_frame_ids = frame_children_map.get(frame["id"], [])
    bounding_box = frame.get("absoluteBoundingBox", {})
    
    error_frame_data = {
        "id": frame["id"],
        "name": frame.get("name", "Unknown"),
        "type": frame.get("type", "FRAME"),
        "children": children_frame_ids,
        "status": ProcessingStatus.FAILED,
        "error_message": error_msg,
        "time_updated": generate_timestamp(),
        "absoluteBoundingBox": bounding_box,
        "visible": frame.get("visible", True),
        "locked": frame.get("locked", False),
        "backgroundColor": frame.get("backgroundColor", ""),
        "dimensions": {
            "width": round(bounding_box.get("width", 800)),
            "height": round(bounding_box.get("height", 600)),
        },
    }
    
    return {k: v for k, v in error_frame_data.items() if v is not None}


def create_metadata(file_key: str, sizes: Dict, total_frames: int, completed_count: int, failed_count: int) -> Dict[str, Any]:
    """Create metadata dictionary"""
    return {
        "fileKey": file_key,
        "sizes": sizes,
        "progress": {
            "total": total_frames,
            "completed": completed_count,
            "failed": failed_count,
        },
        "time_updated": generate_timestamp(),
    }


def create_error_metadata(file_key: str, sizes: Dict, total_frames: int, completed_count: int, failed_count: int, error_msg: str) -> Dict[str, Any]:
    """Create error metadata dictionary"""
    return {
        "fileKey": file_key,
        "sizes": sizes,
        "progress": {
            "total": total_frames,
            "completed": completed_count,
            "failed": failed_count,
        },
        "error": error_msg,
        "time_updated": generate_timestamp(),
    }


async def write_json_file(file_path: str, data: Dict[str, Any]) -> None:
    """Write JSON data to file asynchronously"""
    import aiofiles
    
    async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
        await f.write(json.dumps(data, indent=2, ensure_ascii=False))


@router.post("/add_figma_file_CGA")
async def add_figma_file_CGA(
    background_tasks: BackgroundTasks,
    project_id: str,
    task_id: str,
    figma_link: str,
    current_user=Depends(get_current_user),
):
    """Add Figma file for processing with individual frame storage"""
    try:
        tenant_id = get_tenant_id()
        file_key = extract_file_key(figma_link)

        if not file_key:
            return JSONResponse(
                status_code=400, content={"message": "Invalid Figma link"}
            )

        # Create unique figma_id
        figma_id = f"{tenant_id}-{project_id}-{task_id}-{file_key}"

        # Check if design already exists
        existing_design = await FigmaModel.get_one_CGA(figma_id)
        if existing_design:
            return JSONResponse(
                status_code=400, content={"message": "Design already exists"}
            )

        # Get Figma API key from settings
        settings = await TenantSettings.get_settings(tenant_id)
        figma_api_key = next(
            (
                setting.value
                for setting in settings.integrations.figma
                if setting.name == "figma_api_key"
            ),
            None,
        )
        

        if not figma_api_key:
            return JSONResponse(
                status_code=400, content={"message": "Figma API key not configured"}
            )

        # Get file data and sizes
        async with httpx.AsyncClient() as client:
            data, sizes = await get_figma_file_data_limited_async(
                client, figma_link, figma_api_key, mb_limit=100
            )

        # Determine initial status based on file size
        status = (
            ProcessingStatus.PENDING 
            if sizes["size_kb"] < 6000 
            else ProcessingStatus.FAILED
        )

        # Create user model
        user_model = UserModel(
            username=current_user["sub"],
            name=current_user["custom:Name"],
            email=current_user["email"],
        )

        # Prepare figma data
        figma_data = {
            "tenant_id": tenant_id,
            "project_id": project_id,
            "task_id": task_id,
            "file_key": file_key,
            "id": figma_id,
            "url": figma_link,
            "added_by": user_model.dict(),
            "status": status,
            "total_frames": 0,
            "completed_frames": 0,
            "failed_frames": 0,
            "time_created": generate_timestamp(),
            "time_updated": generate_timestamp(),
            "sizes": FigmaSizesModel(**sizes).dict(),
        }

        # Add to project first
        success = await ProjectModel.add_design_id(project_id, figma_id)
        if not success:
            return JSONResponse(
                status_code=500, content={"message": "Failed to add design to project"}
            )

        # Create design document
        created_design = await FigmaModel.create_CGA(figma_data)
        if not created_design:
            # Cleanup project if design creation fails
            await ProjectModel.remove_design_id(project_id, figma_id)
            return JSONResponse(
                status_code=500, content={"message": "Failed to create design"}
            )

        # Start background processing if file size is acceptable
        if figma_api_key and status != ProcessingStatus.FAILED:
            try:
                background_tasks.add_task(
                    background_process_figma_file_CGA,
                    project_id,
                    figma_link,
                    task_id,
                    tenant_id,
                    figma_api_key,
                    True,
                )
            except Exception as e:
                print(f"Error setting up background task: {str(e)}")

        return JSONResponse(
            status_code=202,
            content={
                "message": "Figma file processing started",
                "status": status,
                "id": figma_id,
            },
        )

    except Exception as e:
        status_code = classify_exception_status_code(e)
        print(f"Error in add_figma_file_CGA: {str(e)}")
        return JSONResponse(
            status_code=status_code,
            content={"message": f"An error occurred: {str(e)}"}
        )
    
import glob

async def get_figma_frame_data(figma_id: str):
    try:
        tenant_id = get_tenant_id()
        s3_handler = S3Handler(tenant_id)
        file_name = f"{figma_id}.json"
        data = s3_handler.get_file(file_name)
        data = json.loads(data.decode("utf-8"))
        frames = data.get('frames', [])
        result = []
        for frame in frames:
            frame_id = frame.get('id')
            frame_name = frame.get('name')
            image_url = frame.get('imageUrl')
            if frame_id and image_url:
                result.append({'frame_id': frame_id, 'image_url': image_url, "frame_name": frame_name})
        return result
    except Exception as e:
        return {'error': str(e)}


@router.get("/list_figma_files_CGA")
async def list_figma_files(
    background_tasks: BackgroundTasks,
    project_id: str,
    file_key: str,
    current_user=Depends(get_current_user),
):
    try:
        tenant_id = get_tenant_id()
        base_path = "/app/data"
        if os.environ.get("LOCAL_DEBUG"):
            base_path = "/tmp"
        figma_id = f"{tenant_id}-{project_id}-{file_key}"
        attachment_path = f"{base_path}/{tenant_id}/{project_id}/workspace/figma_json/{figma_id}"

        # Get frame data
        frame_data_list = await get_figma_frame_data(figma_id=figma_id)
        
        # Handle error case
        if isinstance(frame_data_list, dict) and 'error' in frame_data_list:
            return JSONResponse(
                status_code=500,
                content={"message": f"Error getting frame data: {frame_data_list['error']}"}
            )
        
        # Build lookups for frame_id to image_url and frame_name, and get valid frame_ids
        frame_id_to_image_url = {item['frame_id']: item['image_url'] for item in frame_data_list}
        frame_id_to_frame_name = {item['frame_id']: item['frame_name'] for item in frame_data_list}
        valid_frame_ids = set(frame_id_to_image_url.keys())

        # List JSON files
        json_pattern = os.path.join(attachment_path, "*.json")
        json_files = glob.glob(json_pattern)

        files_with_info = []
        for file_path in json_files:
            filename = os.path.basename(file_path)
            if filename == "metadata.json":
                continue
            
            # Extract frame_id from filename: e.g., figma_{frame_id}.json
            if filename.startswith("figma_") and filename.endswith(".json"):
                frame_id = filename[len("figma_"):-len(".json")]
                
                # Only include files with frame_ids that exist in the frame data
                if frame_id in valid_frame_ids:
                    image_url = frame_id_to_image_url.get(frame_id)
                    frame_name = frame_id_to_frame_name.get(frame_id)
                    files_with_info.append({
                        'filename': filename,
                        'path': file_path,
                        'relative_path': os.path.relpath(file_path, base_path),
                        'frame_id': frame_id,
                        'image_url': image_url,
                        'frame_name': frame_name
                    })

        files_with_info.sort(key=lambda x: x['filename'])
        # try:
        #     background_tasks.add_task(
        #             copy_figma_json_to_attachments,
        #             tenant_id,
        #             project_id,
        #             figma_id
        #         )
        # except Exception as e:
        #     print(f"Error setting up background task: {str(e)}")
        return JSONResponse(
            status_code=200,
            content={
                'files': files_with_info,
                'total_files': len(files_with_info),
                'directory_path': attachment_path,
                'valid_frame_ids': list(valid_frame_ids),  # Optional: for debugging
                'total_valid_frames': len(valid_frame_ids)  # Optional: for debugging
            }
        )
    except Exception as e:
        status_code = classify_exception_status_code(e)
        return JSONResponse(
            status_code=status_code,
            content={"message": f"An error occurred: {str(e)}"}
        )

from app.utils.code_generation_utils import get_codegeneration_path
import shutil

async def copy_figma_json_to_temp_attachments(tenant_id, project_id, figma_id):
    """Copy Figma JSON file from figma_json to temp-attachments directory"""
    base_path = "/efs"
    if os.environ.get("LOCAL_DEBUG"):
        base_path = "/tmp"
    source_path = f"{base_path}/{tenant_id}/{project_id}/workspace/figma_json/{figma_id}"
    code_gen_path = get_codegeneration_path()
    dest_path = f"{code_gen_path}/temp-attachments"
    try:
        if os.path.exists(source_path):
            if not os.path.exists(dest_path):
                os.makedirs(dest_path)
            for file_name in os.listdir(source_path):
                if file_name.endswith(".json"):
                    source_file = os.path.join(source_path, file_name)
                    dest_file = os.path.join(dest_path, file_name)
                    shutil.copy2(source_file, dest_file)
            return True
        else:
            return False
    except Exception as e:
        print(f"Error while copying Figma JSON: {str(e)}")
        return False
from fastapi.responses import FileResponse
@router.get("/download_logs_figma_add/{tenant_id}/{project_id}/{figma_id}")
async def download_logs_figma_add(tenant_id: str, project_id: int, figma_id: str):
    """
    Download the single log file for a specific task.
    Provides a browser download experience for one .log file.
    
    Args:
        tenant_id: Tenant identifier
        project_id: Project ID
        figma_id: figma_id
        
    Returns:
        FileResponse with log file
    """
    try:
        print(f"Tenant_id: {tenant_id}, project_id: {project_id}, figma_id: {figma_id}")

        if os.getenv("LOCAL_DEBUG"):
            log_file = f"/tmp/{tenant_id}/{project_id}/logs/{figma_id}.log"
        else:
            log_file = f"/app/data/{tenant_id}/{project_id}/{figma_id}.log"
        
        # Check if file exists
        if not os.path.exists(log_file):
            raise HTTPException(status_code=404, detail=f"No log file found for task {figma_id}")

        # Create a filename for download
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"logs_task_{figma_id}_{timestamp}.log"

        return FileResponse(
            path=log_file,
            filename=filename,
            media_type="application/octet-stream",
            headers={'Content-Disposition': f'attachment; filename="{filename}"'}
        )
    
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error serving log file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to serve log file: {str(e)}")
    
@router.post("/upload_figma_files_db_v2")
async def upload_figma_files_db_v2(
    background_tasks: BackgroundTasks,
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
    task_id = None
):
    from datetime import datetime

    # Get tenant_id and file_key early for logger setup
    tenant_id = get_tenant_id()
    file_key = extract_file_key(figma_link.url)

    if not file_key:
        return JSONResponse(
            status_code=400, content={"message": "Invalid Figma link"}
        )

    figma_id = f"{tenant_id}-{project_id}-{file_key}"

    # Setup centralized logger
    figma_logger, log_file_path = setup_figma_logger(project_id,tenant_id, figma_id,task_id)

    request_id = f"Add_figma_file"
    figma_logger.info(f"[{request_id}] Starting add_figma_file_v2 endpoint")
    figma_logger.info(f"[{request_id}] Log file: {log_file_path}")
    figma_logger.info(f"[{request_id}] Input parameters - project_id: {project_id}, figma_url: {figma_link.url}, figma_name: {figma_link.name}")

    try:
        # Step 1: Get tenant ID (already done above)
        figma_logger.info(f"[{request_id}] Step 1: Getting tenant ID")
        figma_logger.info(f"[{request_id}] Step 1 SUCCESS: tenant_id = {tenant_id}")

        # Step 2: Extract file key from Figma URL (already done above)
        figma_logger.info(f"[{request_id}] Step 2: Extracting file key from URL: {figma_link.url}")
        figma_logger.info(f"[{request_id}] Step 2 SUCCESS: file_key = {file_key}")

        # Step 3: Create figma_id (already done above)
        figma_logger.info(f"[{request_id}] Step 3: Created figma_id = {figma_id}")

        # Step 4: Check if design already exists
        figma_logger.info(f"[{request_id}] Step 4: Checking if design already exists")
        existing_design = await FigmaModel.get_one(figma_id)
        if existing_design:
            figma_logger.warning(f"[{request_id}] Step 4 FAILED: Design already exists with id {figma_id}")
            return JSONResponse(
                status_code=400, content={"message": "Design already exists"}
            )
        figma_logger.info(f"[{request_id}] Step 4 SUCCESS: Design does not exist, proceeding")

        # Step 5: Create UserModel
        figma_logger.info(f"[{request_id}] Step 5: Creating UserModel from current_user")
        user_model = UserModel(
            username=current_user["sub"],
            name=current_user["custom:Name"],
            email=current_user["email"],
        )
        figma_logger.info(f"[{request_id}] Step 5 SUCCESS: UserModel created for user {user_model.username}")

        # Step 6: Get tenant settings
        figma_logger.info(f"[{request_id}] Step 6: Getting tenant settings")
        settings = await TenantSettings.get_settings(tenant_id)
        figma_logger.info(f"[{request_id}] Step 6 SUCCESS: Retrieved tenant settings")

        # Step 7: Extract Figma API key
        figma_logger.info(f"[{request_id}] Step 7: Extracting Figma API key from settings")
        figma_api_key = next(
            (
                setting.value
                for setting in settings.integrations.figma
                if setting.name == "figma_api_key"
            ),
            None,
        )

        if not figma_api_key:
            figma_logger.error(f"[{request_id}] Step 7 FAILED: Figma API key not configured in tenant settings")
            return JSONResponse(
                status_code=400, content={"message": "Figma API key not configured"}
            )
        figma_logger.info(f"[{request_id}] Step 7 SUCCESS: Figma API key found (length: {len(figma_api_key)})")

        # Step 8: Fetch Figma file data
        figma_logger.info(f"[{request_id}] Step 8: Fetching Figma file data with httpx client")
        async with httpx.AsyncClient() as client:
            figma_logger.info(f"[{request_id}] Step 8a: Created httpx AsyncClient")
            # Process Figma file data first to get total frames count
            data, sizes = await get_figma_file_data_limited_async(
                client, figma_link.url, figma_api_key, mb_limit=100
            )
            figma_logger.info(f"[{request_id}] Step 8b SUCCESS: Retrieved Figma data - size_kb: {sizes.get('size_kb', 'unknown')}, size_mb: {sizes.get('size_mb', 'unknown')}")
        frames_list = []
        screens = []

        document_children = data['document']['children']
        canvas_nodes = [child for child in document_children if child['type'] == 'CANVAS']

        # Extract frames from each canvas/page
        for canvas in canvas_nodes:
            page_name = canvas.get('name', 'Unnamed Page')
            canvas_id = canvas.get('id')
            
            top_level_frames = [child for child in canvas['children'] if child['type'] == 'FRAME']
            
            for frame in top_level_frames:
                frame_info = {
                    'frame_id': frame['id'],
                    'frame_name': frame['name'],
                    'page_name': page_name,
                    'canvas_id': canvas_id
                }
                frames_list.append(frame_info)  
                screen_obj = {
                    "screen_id": frame['id'],
                    "screen_name": frame['name'],
                    "canvas": page_name,
                    "canvas_id": canvas_id,
                    "status": "extracted"  # Default status
                }
                screens.append(screen_obj)
        # Step 9: Determine processing status
        figma_logger.info(f"[{request_id}] Step 9: Determining processing status")
        # if(sizes["size_kb"] < 6000):
        #     status = ProcessingStatus.PENDING
        # else:
        #     status = ProcessingStatus.FAILED
        status = ProcessingStatus.PENDING
        figma_logger.info(f"[{request_id}] Step 9 SUCCESS: Status set to {status}")

        # Step 10: Create figma_data dictionary
        figma_logger.info(f"[{request_id}] Step 10: Creating figma_data dictionary")
        figma_data = {
            "tenant_id": tenant_id,
            "project_id": project_id,
            "file_key": file_key,
            "id": figma_id,
            "name": figma_link.name,
            "url": figma_link.url,
            "added_by": user_model.dict(),
            "status": status,
            "total_frames": 0,
            "completed_frames": 0,
            "failed_frames": 0,
            "time_created": generate_timestamp(),
            "time_updated": generate_timestamp(),
            "sizes": FigmaSizesModel(**sizes).dict(),
        }
        figma_logger.info(f"[{request_id}] Step 10 SUCCESS: figma_data dictionary created")

        # Step 11: Add design to project
        figma_logger.info(f"[{request_id}] Step 11: Adding design to project {project_id}")
        success = await ProjectModel.add_design_id(project_id, figma_id)
        if not success:
            figma_logger.error(f"[{request_id}] Step 11 FAILED: Failed to add design to project")
            return JSONResponse(
                status_code=500, content={"message": "Failed to add design to project"}
            )
        figma_logger.info(f"[{request_id}] Step 11 SUCCESS: Design added to project")

        # Step 12: Create design document in database
        figma_logger.info(f"[{request_id}] Step 12: Creating design document in database")
        created_design = await FigmaModel.create(figma_data)
        if not created_design:
            figma_logger.error(f"[{request_id}] Step 12 FAILED: Failed to create design document")
            # Cleanup project if design creation fails
            figma_logger.info(f"[{request_id}] Step 12 CLEANUP: Removing design from project due to creation failure")
            await ProjectModel.remove_design_id(project_id, figma_id)
            return JSONResponse(
                status_code=500, content={"message": "Failed to create design"}
            )
        figma_logger.info(f"[{request_id}] Step 12 SUCCESS: Design document created in database")
        try:
            # Create the document structure for figma_datas collection
            figma_datas_document = {
                "project_id": project_id,
                "task_id": task_id if task_id else f"cg_{generate_timestamp()}",  # Generate task_id if not provided
                "figma_url": figma_link.url,
                "file_key": file_key,
                "figma_id": figma_id,
                "screens": screens
            }
            mongo_handler = get_mongo_db(
            db_name=(settings.MONGO_DB_NAME), collection_name="figma_datas"
            )
            inserted_id = await mongo_handler.insert(figma_datas_document, db=mongo_handler.db,)
            
            figma_logger.info(f"[{request_id}] Step 13 SUCCESS: Inserted document with ID: {inserted_id}")
            
        except Exception as e:
            figma_logger.error(f"[{request_id}] Step 13 ERROR: Failed to insert into figma_datas collection: {str(e)}")
            # You might want to decide whether this should be a fatal error or just logged
            print(f"Error inserting into figma_datas: {str(e)}")

        # Step 13: Setup background task
        figma_logger.info(f"[{request_id}] Step 13: Setting up background task")
    
        # Step 14: Return success response
        figma_logger.info(f"[{request_id}] Step 14: Returning success response")
        response_content = {
            "message": "Figma file processing started",
            "status": ProcessingStatus.PENDING,
            "id": figma_id,
            "frames_list":frames_list
        }
        figma_logger.info(f"[{request_id}] Step 14 SUCCESS: Endpoint completed successfully - figma_id: {figma_id}")

        return JSONResponse(
            status_code=202,
            content=response_content,
        )

    except Exception as e:
        status_code = classify_exception_status_code(e)
        figma_logger.error(f"[{request_id}] ENDPOINT FAILED: Exception occurred - {str(e)}")
        figma_logger.error(f"[{request_id}] Exception type: {type(e).__name__}")
        figma_logger.error(f"[{request_id}] Status code determined: {status_code}")
        print(f"Error in add_figma_file_v2: {str(e)}")  # Debug print
        return JSONResponse(
            status_code=status_code,
            content={"message": f"An error occurred: {str(e)}"}
        )
from app.utils.figma_screen_design_process import create_design_system, create_screens_by_frame_ids_with_images, get_figma_image_url

async def get_figma_image_url_oauth(frame_id: Union[str, List[str]], 
                                   access_token: str, 
                                   file_key: str) -> Dict[str, str]:
    """
    Fetch Figma image URLs using OAuth access token instead of API key.
    """
    try:
        print(f"Fetching images with OAuth token for file_key: {file_key}")
        frame_ids = frame_id if isinstance(frame_id, list) else [frame_id]
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            url = f"https://api.figma.com/v1/images/{file_key}"
            params = {
                "ids": ",".join(frame_ids),
                "scale": 2,
                "format": "png"
            }
            headers = {"Authorization": f"Bearer {access_token}"}

            response = await client.get(url, params=params, headers=headers)
            
            if response.status_code == 200:
                images = response.json().get('images', {})
                print(f"Successfully fetched {len(images)} images with OAuth")
                return {fid: images.get(fid, "") for fid in frame_ids if images.get(fid)}
            elif response.status_code == 403:
                print("Access denied to images - insufficient permissions")
                return {}
            elif response.status_code == 404:
                print("Images not found - file or nodes may not exist")
                return {}
            else:
                print(f"HTTP {response.status_code} error fetching images")
                return {}
                
    except Exception as e:
        print(f"❌ Error retrieving image URL(s) with OAuth: {str(e)}")
        return {}
@router.post("/add_figma_data")
async def add_figma_data(
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
    task_id=None
):
    import os
    import asyncio
    from datetime import datetime

    # Get tenant_id and file_key early for logger setup
    tenant_id = get_tenant_id()
    file_key = extract_file_key(figma_link.url)

    if not file_key:
        return JSONResponse(
            status_code=400, content={"message": "Invalid Figma link"}
        )

    figma_id = f"{tenant_id}-{project_id}-{file_key}"
    
    try:
        mongo_handler = get_mongo_db(collection_name="figma_datas")
    except Exception as e:
        return JSONResponse(
            status_code=400, 
            content={"message": f"Error while getting data from mongo db {str(e)}"}
        )

    try:
        # Check if data already exists in MongoDB
        existing_data = await mongo_handler.get_one({
            "figma_id": figma_id,
            "project_id": project_id
        }, db=mongo_handler.db)

        if existing_data:
            # Return existing data
            response_content = {
                "message": "Figma data retrieved from cache",
                "id": figma_id,
                "figma_link": figma_link.url,
                "screen_list": existing_data.get("screen_list", []),
                "design_system_path": existing_data.get("design_system_path",''),
                "design_name": existing_data.get("design_name",'')
            }
            return JSONResponse(status_code=200, content=response_content)

        # Data doesn't exist, fetch from Figma API
        settings = await TenantSettings.get_settings(tenant_id)
        figma_api_key = next(
            (
                setting.value
                for setting in settings.integrations.figma
                if setting.name == "figma_api_key"
            ),
            None,
        )

        # Try to fetch data using API key first (for public files)
        data = None
        sizes = None
        access_token = None
        api_key_error = None
        
        if figma_api_key:
            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    data, sizes = await get_figma_file_data_limited_async(
                        client, figma_link.url, figma_api_key, mb_limit=100
                    )
                print("Successfully fetched data using API key")
            except Exception as api_key_error:
                print(f"API key approach failed: {str(api_key_error)}")
                # Continue to try OAuth approach
        
        # If API key approach failed or no API key, try OAuth credentials
        if not data:
            try:
                user_id = current_user.get("sub")
                credentials = await figma_oauth_manager.get_credentials(tenant_id, user_id)
                
                if not credentials:
                    if not figma_api_key:
                        return JSONResponse(
                            status_code=400, 
                            content={"message": "Neither Figma API key nor OAuth credentials configured. Please configure API key for public files or connect OAuth for private files."}
                        )
                    else:
                        return JSONResponse(
                            status_code=400, 
                            content={"message": "Failed to fetch Figma data with API key and no OAuth credentials found. Please connect your Figma account for private files."}
                        )
                
                # Validate OAuth token
                access_token = credentials.get("access_token")
                if not validate_figma_token(access_token):
                    await figma_oauth_manager.deactivate_credentials(tenant_id, user_id)
                    return JSONResponse(
                        status_code=401, 
                        content={"message": "OAuth token is invalid or expired. Please reconnect your Figma account."}
                    )
                
                # Fetch data using OAuth token
                async with httpx.AsyncClient(timeout=30.0) as client:
                    headers = {"Authorization": f"Bearer {access_token}"}
                    file_url = f"https://api.figma.com/v1/files/{file_key}"
                    response = await client.get(file_url, headers=headers, timeout=30.0)
                    
                    if response.status_code == 200:
                        data = response.json()
                        # Calculate sizes
                        json_str = json.dumps(data)
                        size_bytes = len(json_str.encode("utf-8"))
                        size_kb = size_bytes / 1024
                        size_mb = size_kb / 1024
                        sizes = {
                            "size_kb": round(size_kb, 2),
                            "size_mb": round(size_mb, 2),
                            "byte_limit": None,
                            "mb_limit": None,
                        }
                        print("Successfully fetched data using OAuth token")
                    elif response.status_code == 403:
                        return JSONResponse(
                            status_code=403, 
                            content={"message": "Access denied to this file. You may not have permission to view it."}
                        )
                    elif response.status_code == 404:
                        return JSONResponse(
                            status_code=404, 
                            content={"message": "Access denied to this file. You may not have permission to view it."}
                        )
                    else:
                        return JSONResponse(
                            status_code=500, 
                            content={"message": f"Failed to fetch Figma data: HTTP {response.status_code}"}
                        )
                        
            except Exception as oauth_error:
                print(f"OAuth approach failed: {str(oauth_error)}")
                if not figma_api_key:
                    return JSONResponse(
                        status_code=500, 
                        content={"message": f"Failed to fetch Figma data: {str(oauth_error)}"}
                    )
                else:
                    return JSONResponse(
                        status_code=500, 
                        content={"message": f"Both API key and OAuth approaches failed. API key error: {str(api_key_error)}, OAuth error: {str(oauth_error)}"}
                    )
        figma_name = data.get('name','')
        s3_handler = S3Handler(tenant_id)
        await s3_handler.update_file_async(
            f"{figma_id}.json", json.dumps(data)
        )

        base_path = "/app/data"
        if os.environ.get("LOCAL_DEBUG"):
            base_path = "/tmp"
        output_directory = f"{base_path}/{tenant_id}/{project_id}/workspace/figmafiles"
        design_system_path = create_design_system(data, output_directory,figma_id)
        # Process frames data and download images
        frames_list, image_mappings = await _extract_frames_from_figma_data(
            data=data,
            figma_api_key=figma_api_key,
            access_token=access_token,
            project_id=project_id,
            file_key=file_key,
            task_id=task_id
        )
        

        # Prepare document for MongoDB
        figma_document = {
            "figma_id": figma_id,
            'design_name': figma_name,
            "project_id": project_id,
            "task_id": task_id,
            "figma_url": figma_link.url,
            "screen_list": frames_list,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "tenant_id": tenant_id,
            "file_key": file_key,
            "design_system_path":design_system_path
        }

        # Store in MongoDB
        await mongo_handler.insert(figma_document, db=mongo_handler.db)


        # Return response (same format as original add_figma_data)
        response_content = {
            "message": "Figma file processing completed",
            "id": figma_id,
            'design_name': figma_name,
            "figma_link": figma_link.url,
            "screen_list": frames_list,
            "design_system_path": design_system_path,
            "auth_method": "oauth" if access_token else "api_key"
        }

        return JSONResponse(status_code=201, content=response_content)

    except HTTPException as he:
        raise he
    except Exception as e:
        status_code = classify_exception_status_code(e)
        print(f"Error in add_figma_data: {str(e)}")
        import traceback
        traceback.print_exc()
        
        return JSONResponse(
            status_code=status_code,
            content={"message": f"An error occurred: {str(e)}"}
        )
 

async def _extract_frames_from_figma_data(data: dict, figma_api_key: str = None, access_token: str = None, project_id: str = None, file_key: str = None, task_id: str = None) -> list:
    """
    Extract frames information from Figma API response data WITH images.
    Downloads images to attachments folder.
    """
    frames_list = []
    all_image_mappings = {}  # Collect all image URL to path mappings
    
    try:
        document_children = data.get('document', {}).get('children', [])
        canvas_nodes = [child for child in document_children if child.get('type') == 'CANVAS']

        # Collect all frame IDs first
        all_frame_ids = []
        for canvas in canvas_nodes:
            canvas_children = canvas.get('children', [])
            top_level_frames = [child for child in canvas_children if child.get('type') == 'FRAME']
            all_frame_ids.extend([frame.get('id') for frame in top_level_frames])

        # Fetch all images at once (more efficient)
        image_urls = {}
        if all_frame_ids and (figma_api_key or access_token):
            if access_token:
                image_urls = await get_figma_image_url_oauth(all_frame_ids, access_token, file_key)
            elif figma_api_key:
                # Use API key method
                image_urls = await get_figma_image_url(all_frame_ids, figma_api_key, file_key)

        # Setup assets directory using proper path construction
        tenant_id = get_tenant_id()
        base_path = "/app/data"
        if os.environ.get("LOCAL_DEBUG"):
            base_path = "/tmp"
        attachments_dir = f"{base_path}/{tenant_id}/{project_id}/workspace/assets/figmaimages"

        # Extract frames from each canvas/page
        for canvas in canvas_nodes:
            page_name = canvas.get('name', 'Unnamed Page')
            canvas_id = canvas.get('id')
            
            canvas_children = canvas.get('children', [])
            top_level_frames = [child for child in canvas_children if child.get('type') == 'FRAME']
            
            for frame in top_level_frames:
                frame_id = frame.get('id')
                image_url = image_urls.get(frame_id, "")
                
                # Just collect frame metadata - NO IMAGE DOWNLOAD yet
                # Images will be downloaded only when screens are selected for code generation
                frame_info = {
                    'screen_id': frame_id,
                    'screen_name': frame.get('name'),
                    'Canvas': page_name,
                    'canvas_id': canvas_id,
                    "processed": False,
                    "image_url": image_url,
                    "local_image_path": None,  # Will be set when downloaded
                    "embedded_images": []  # Will be populated when downloaded
                }
                frames_list.append(frame_info)
                
    except KeyError as e:
        print(f"Error extracting frames: Missing key {str(e)}")
        raise ValueError(f"Invalid Figma data structure: {str(e)}")
    
    print(f"✅ Extracted {len(frames_list)} frames metadata (images will be downloaded when screens are selected)")
    return frames_list, {}

class FigmaScreenRequestModel(BaseModel):
    selected_screen_ids: List = Field(default=[])
    figma_id: str = Field(default="")


@router.post("/writing_screen_json_files")
async def writing_screen_json_files(
    project_id: str,
    screens: FigmaScreenRequestModel,
    task_id: str,
    current_user=Depends(get_current_user),
    mongo_db: MongoDBHandler = Depends(get_mongo_db),
):
    """
    Process screen JSON files with 60-second timeout handling.
    """
    try:
        selected_screen_ids = screens.selected_screen_ids
        tenant_id = get_tenant_id()
        figma_id = screens.figma_id

        print(f"🚀 Starting screen processing for {len(selected_screen_ids)} screens with 60s timeout")

        # Wrap the entire processing logic with a 60-second timeout
        try:
            result = await asyncio.wait_for(
                _process_screens_with_timeout(
                    project_id, screens, task_id, tenant_id, figma_id, selected_screen_ids, mongo_db
                ),
                timeout=60.0  # 60 seconds timeout
            )
            return result

        except asyncio.TimeoutError:
            print(f"⏰ Screen processing timed out after 60 seconds")
            return JSONResponse(
                status_code=408,  # Request Timeout
                content={
                    "message": "Screen processing timed out after 60 seconds",
                    "figma_id": figma_id,
                    "selected_screen_ids": selected_screen_ids,
                    "task_id": task_id,
                    "timeout_seconds": 60,
                    "suggestion": "Try processing fewer screens at once or check Figma API connectivity"
                }
            )

    except Exception as e:
        print(f"❌ Error in screen processing: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"message": f"Error processing screen JSON files: {str(e)}"}
        )


async def _process_screens_with_timeout(
    project_id: str,
    screens: FigmaScreenRequestModel,
    task_id: str,
    tenant_id: str,
    figma_id: str,
    selected_screen_ids: List[str],
    mongo_db: MongoDBHandler
):
    """
    The actual processing logic wrapped for timeout handling.
    """
    # Get file from S3
    file_name = f"{figma_id}.json"
    s3_handler = S3Handler(tenant_id)
    data = s3_handler.get_file(file_name)
    data = json.loads(data.decode("utf-8"))

    # Set up paths
    base_path = "/tmp" if os.environ.get("LOCAL_DEBUG") else "/app/data"
    design_system_path = f"{base_path}/{tenant_id}/{project_id}/workspace/figmafiles/design_system_{figma_id}.json"
    print(f"design_system_path = {design_system_path}")
    output_directory = f"{base_path}/{tenant_id}/{project_id}/workspace/temp-attachments/"
    print(f"output_directory: {output_directory}")

    # Process screens
    settings = await TenantSettings.get_settings(tenant_id)
    figma_api_key = next(
        (
            setting.value
            for setting in settings.integrations.figma
            if setting.name == "figma_api_key"
        ),
        None,
    )
    file_key = extract_file_key_from_figma_id(figma_id)

    # Get image mappings by processing ALL images from ALL selected screens at once
    screen_image_mappings = {}
    node_to_url_mappings = {}
    if figma_api_key:
        # Process images for selected screens to get URL mappings
        assets_dir = f"{base_path}/{tenant_id}/{project_id}/workspace/assets/figmaimages"
        print(f"assets_dir: {assets_dir}")
        # Collect ALL frames from selected screens
        all_selected_frames = []
        for frame_id in selected_screen_ids:
            frame_data = None
            # Find the frame in the Figma data
            for canvas in data.get('document', {}).get('children', []):
                if canvas.get('type') == 'CANVAS':
                    for frame in canvas.get('children', []):
                        if frame.get('id') == frame_id and frame.get('type') == 'FRAME':
                            frame_data = frame
                            break
                    if frame_data:
                        break

            if frame_data:
                all_selected_frames.append(frame_data)
                print(f"📋 Found screen {frame_id} ({frame_data.get('name', 'unnamed')})")
            else:
                print(f"⚠️  Frame data not found for screen {frame_id}")

        # Process each screen separately for proper deduplication within each screen
        if all_selected_frames:
            print(f"🖼️  Processing images from {len(all_selected_frames)} screens individually...")

            all_saved_images = []
            all_screen_image_mappings = {}
            all_node_to_url_mappings = {}

            for i, frame_data in enumerate(all_selected_frames):
                frame_id = frame_data.get('id', f'frame_{i}')
                frame_name = frame_data.get('name', f'Screen {i+1}')
                print(f"📱 Processing screen {i+1}/{len(all_selected_frames)}: {frame_name} (id: {frame_id})")

                # Create document for this single screen
                single_screen_document = {
                    "document": {
                        "children": [frame_data]
                    }
                }

                try:
                    # Add timeout for individual image processing to prevent hanging
                    saved_images, screen_mappings, node_mappings = await asyncio.wait_for(
                        process_and_save_images(
                            single_screen_document,
                            file_key,
                            assets_dir,
                            figma_api_key,
                            None  # access_token
                        ),
                        timeout=30.0  # 30 seconds per screen for image processing
                    )

                    # Collect all results
                    all_saved_images.extend(saved_images)
                    all_screen_image_mappings.update(screen_mappings)
                    all_node_to_url_mappings.update(node_mappings)

                    print(f"✅ Screen {frame_name}: {len(saved_images)} images, {len(screen_mappings)} mappings")

                except asyncio.TimeoutError:
                    print(f"⏰ Timeout processing images for screen {frame_name} (30s limit)")
                except Exception as e:
                    print(f"❌ Warning: Failed to process images for screen {frame_name}: {str(e)}")

            # Update final variables
            saved_images = all_saved_images
            screen_image_mappings = all_screen_image_mappings
            node_to_url_mappings = all_node_to_url_mappings

            print(f"✅ Total: {len(saved_images)} images, {len(screen_image_mappings)} mappings from {len(all_selected_frames)} screens")

    print(f"🎯 Final image mappings collected: {len(screen_image_mappings)} total mappings")
    print(f"📊 These mappings will be applied to {len(selected_screen_ids)} screens for consistent imagePath values")
    if screen_image_mappings:
        print("📋 Sample image mappings:")
        for i, (url, path) in enumerate(list(screen_image_mappings.items())[:3]):
            print(f"   {i+1}. {url[:50]}... → {path}")
    else:
        print("⚠️  No image mappings found - screens will not have imagePath fields")

    # Add timeout for screen creation
    screens_path = await asyncio.wait_for(
        create_screens_by_frame_ids_with_images(
            data,
            selected_screen_ids,
            design_system_path,
            output_directory,
            figma_api_key,
            file_key,
            screen_image_mappings,  # Pass the image mappings
            node_to_url_mappings  # Pass node-to-URL mappings
        ),
        timeout=20.0  # 20 seconds for screen creation
    )

    # Update MongoDB - Mark selected screens as processed AND add image data
    try:
        mongo_handler = get_mongo_db(collection_name="figma_datas")

        # Get the current document first document
        current_doc = await mongo_handler.get_one({
            "figma_id": figma_id,
            "project_id": project_id
        }, db=mongo_handler.db)

        if current_doc and "screen_list" in current_doc:
            # Update the screen_list locally based on the screen_id
            updated_screen_list = current_doc["screen_list"]
            for screen in updated_screen_list:
                if screen.get("screen_id") in selected_screen_ids:
                    screen["processed"] = True
                    # Add image data for the selected screen
                    screen["embedded_images"] = saved_images
                    # Find main screen image path from saved_images
                    main_screen_image = None
                    for img in saved_images:
                        if img.get('id') == screen.get("screen_id"):
                            main_screen_image = img.get('local_path')
                            break
                    if main_screen_image:
                        screen["local_image_path"] = main_screen_image

            # Update the entire document with modifiedS
            update_result = await mongo_handler.update_one(
                filter={
                    "figma_id": figma_id,
                    "project_id": project_id
                },
                element={
                    "screen_list": updated_screen_list,
                    "updated_at": datetime.utcnow()
                },
                db=mongo_handler.db
            )

            print(f"MongoDB update completed for screens: {selected_screen_ids} with image data")
        else:
            print("Warning: Document not found or missing screen_list")

    except Exception as mongo_error:
        print(f"Warning: Failed to update MongoDB processed status: {str(mongo_error)}")
        # Don't fail the entire request if MongoDB update fails

    # Add timeout for sync operation
    sync_response = await asyncio.wait_for(
        sync_attachements(task_id, project_id, mongo_db),
        timeout=10.0  # 10 seconds for sync
    )

    return JSONResponse(
        status_code=200,
        content={
            "message": "Screen JSON files created successfully",
            "screens_path": screens_path,
            "processed_screen_ids": selected_screen_ids,
            "sync_response": sync_response,
            "design_system_path": design_system_path
        }
    )



async def sync_attachements(
    task_id: str,
    project_id:str,
    mongo_db
):
    if os.environ.get("LOCAL_DEBUG"):
        response = {"error": f"Sync up in local is not available now"}
        return response
    try:
        tenant_id = get_tenant_id()
        query_filter = {"job_id": task_id}
        cursor = mongo_db.db[TASKS_COLLECTION_NAME].find_one(
            query_filter, 
            {"pod_name": 1,"_id": 0}
        )
        pod_prefix = cursor.get("pod_name")
        print(f"Pod_prefix: {pod_prefix}")
        stage = get_stage(settings)
        stage_params = 'dev' if stage == 'develop' else stage
        code_gen_url = get_codegen_url(stage_params, pod_prefix=pod_prefix)
        print(code_gen_url)
        url = f'{code_gen_url}/sync_attachments'
        params = f'?task_id={task_id}&project_id={project_id}&tenant_id={tenant_id}'
        url = url + params
        response = requests.get(url, verify=False, timeout=10)
        response = response.json()
        return response
    except Exception as e:
        response = {"error": f"Error while sync up {e}"}
        print(f"error while sync up {e}")
        return response





def extract_file_key_from_figma_id(figma_id: str) -> str:
    """
    Extract file key from figma_id format: tenant_id-project_id-file_key
    """
    try:
        parts = figma_id.split('-')
        if len(parts) >= 3:
            # file_key is everything after the second dash
            return '-'.join(parts[2:])
        return None
    except Exception:
        return None
    

@router.get("/get_figma_designs")
async def get_figma_designs(
    project_id: str,
    current_user=Depends(get_current_user),
    figma_id: str = None
):
    """
    Retrieve Figma designs for a specific project or a specific figma design.
    
    Args:
        project_id (str): The project ID to filter designs
        figma_id (str, optional): The specific figma_id to retrieve
        current_user: Authenticated user dependency
        
    Returns:
        JSONResponse: List of figma designs or single figma design with all fields
    """
    from datetime import datetime
    import json
    from bson import ObjectId
    
    def json_serializer(obj):
        """JSON serializer for objects not serializable by default json code"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        if isinstance(obj, ObjectId):
            return str(obj)
        raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")
    
    try:
        tenant_id = get_tenant_id()
        
        # Get MongoDB handler
        mongo_handler = get_mongo_db(collection_name="figma_datas")
        
        # Query filter - conditional based on figma_id parameter
        query_filter = {
            "project_id": project_id,
            "tenant_id": tenant_id
        }
        
        if figma_id:
            query_filter["figma_id"] = figma_id
            
            # Get single design
            design = mongo_handler.db[mongo_handler.collection].find_one(query_filter)
            
            if not design:
                return JSONResponse(
                    status_code=404,
                    content={"message": "Figma design not found"}
                )
                
            # Convert to JSON-serializable format
            design_json = json.loads(json.dumps(design, default=json_serializer))
            
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Figma design retrieved successfully",
                    "data": design_json
                }
            )
        else:
            # Get all designs for the project with sorting and projection for limited fields
            cursor = mongo_handler.db[mongo_handler.collection].find(
                query_filter, 
                {"figma_id": 1, "design_name": 1, "_id": 0}
            ).sort("created_at", -1)
            designs = list(cursor)
            
            # Handle empty results
            if not designs:
                return JSONResponse(
                    status_code=200,
                    content={
                        "message": "No Figma designs found for this project",
                        "data": [],
                        "count": 0
                    }
                )
            
            # Return successful response (no need for JSON serialization as we're only returning strings)
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Figma designs retrieved successfully",
                    "data": designs,
                    "count": len(designs)
                }
            )
        
    except Exception as e:
        status_code = classify_exception_status_code(e)
        print(f"Error in get_figma_designs: {str(e)}")
        import traceback
        traceback.print_exc()
        
        return JSONResponse(
            status_code=status_code,
            content={"message": f"An error occurred while fetching Figma designs: {str(e)}"}
        )
@router.get("/list_figma_files_screens")
async def list_figma_files_screens(
    file_key: str,
    project_id: str,
    screen_ids: List[str] = Query(...),
    current_user=Depends(get_current_user),
):
    try:
        tenant_id = get_tenant_id()
        user_id = current_user.get("sub")

        # Get Figma API key from settings
        settings = await TenantSettings.get_settings(tenant_id)
        figma_api_key = next(
            (
                setting.value
                for setting in settings.integrations.figma
                if setting.name == "figma_api_key"
            ),
            None,
        )

        # Try to fetch images using API key first (for public files)
        image_response = {}
        api_key_error = None
        
        if figma_api_key:
            try:
                image_response = await get_figma_image_url(
                    screen_ids,
                    figma_api_key,
                    file_key
                )
                print("Successfully fetched images using API key")
            except Exception as e:
                api_key_error = str(e)
                print(f"API key approach failed: {api_key_error}")
                # Continue to try OAuth approach

        # If API key approach failed or returned empty results, try OAuth credentials
        if not image_response:
            try:
                credentials = await figma_oauth_manager.get_credentials(tenant_id, user_id)
                
                if not credentials:
                    if not figma_api_key:
                        return JSONResponse(
                            status_code=400, 
                            content={"message": "Neither Figma API key nor OAuth credentials configured. Please configure API key for public files or connect OAuth for private files."}
                        )
                    else:
                        return JSONResponse(
                            status_code=400, 
                            content={"message": "Failed to fetch images with API key and no OAuth credentials found. Please connect your Figma account for private files."}
                        )
                
                # Validate OAuth token
                access_token = credentials.get("access_token")
                if not validate_figma_token(access_token):
                    await figma_oauth_manager.deactivate_credentials(tenant_id, user_id)
                    return JSONResponse(
                        status_code=401, 
                        content={"message": "OAuth token is invalid or expired. Please reconnect your Figma account."}
                    )
                
                # Fetch images using OAuth token
                image_response = await get_figma_image_url_oauth(
                    screen_ids,
                    access_token,
                    file_key
                )
                print("Successfully fetched images using OAuth token")
                        
            except Exception as oauth_error:
                print(f"OAuth approach failed: {str(oauth_error)}")
                if not figma_api_key:
                    return JSONResponse(
                        status_code=500, 
                        content={"message": f"Failed to fetch images: {str(oauth_error)}"}
                    )
                else:
                    return JSONResponse(
                        status_code=500, 
                        content={"message": f"Both API key and OAuth approaches failed. API key error: {api_key_error}, OAuth error: {str(oauth_error)}"}
                    )

        return JSONResponse(
            status_code=200,
            content=image_response
        )

    except Exception as e:
        import traceback
        traceback.print_exc()
        status_code = classify_exception_status_code(e)
        return JSONResponse(
            status_code=status_code,
            content={"message": f"An error occurred: {str(e)}"}
        )

# ============================================================================
# FIGMA OAUTH ENDPOINTS 
# ============================================================================

from app.utils.figma_oauth_utils import (
    get_figma_client_details,
    exchange_figma_code_for_token,
    get_figma_user_info,
    validate_figma_token
)
from app.models.figma_oauth_model import figma_oauth_manager
import secrets
from datetime import datetime


@router.get("/oauth/login")
async def figma_oauth_login(
    userId: str = Query(..., description="User ID"),
    tenantId: str = Query(..., description="Tenant ID"),
    current_user=Depends(get_current_user)
):
    """
    Redirect the user to Figma's OAuth page.
    Following the same pattern as GitHub OAuth.
    """
    try:
        # Load Figma OAuth details
        FIGMA_CLIENT_ID, FIGMA_CLIENT_SECRET, FIGMA_REDIRECT_URI = get_figma_client_details()
        
        # Generate state for security
        state = secrets.token_urlsafe(32)
        
        # Generate code verifier for PKCE
        code_verifier = secrets.token_urlsafe(64)
        
        # Create OAuth session
        from app.models.figma_oauth_model import figma_oauth_manager, FigmaOAuthSession
        
        session_data = FigmaOAuthSession(
            tenant_id=tenantId,
            user_id=userId,
            state=state,
            code_verifier=code_verifier
        )
        
        # Save session to database
        session_saved = await figma_oauth_manager.save_oauth_session(session_data)
        if not session_saved:
            raise HTTPException(status_code=500, detail="Failed to create OAuth session")
        
        # Build authorization URL with PKCE
        import hashlib
        import base64
        
        # Generate code challenge from code verifier
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode()).digest()
        ).decode().rstrip('=')
        
        figma_auth_url = (
            f"https://www.figma.com/oauth"
            f"?client_id={FIGMA_CLIENT_ID}"
            f"&scope=files:read,file_comments:write,webhooks:write"
            f"&state={state}"
            f"&response_type=code"
            f"&redirect_uri={FIGMA_REDIRECT_URI}"
            f"&code_challenge={code_challenge}"
            f"&code_challenge_method=S256"
        )
        
        print(f"Figma Auth URL: {figma_auth_url}")
        print(f"OAuth session created with state: {state}")
        
        return {"url": figma_auth_url}  # Return the URL as part of a JSON response
        
    except Exception as e:
        print(f"Error in OAuth login: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to initiate OAuth flow: {str(e)}")



@router.get("/oauth/callback")
async def figma_oauth_callback(request: Request):
    """
    Figma callback URL to exchange code for an access token.
    Updated to handle PKCE flow properly.
    """
    try:
        code = request.query_params.get("code")
        state = request.query_params.get("state") 
        userId = request.query_params.get("userId")
        tenant_id = request.query_params.get("tenantId")
        
        print(f"Figma OAuth callback received - code: {code}, state: {state}, userId: {userId}, tenantId: {tenant_id}")
        
        if not code:
            raise HTTPException(status_code=400, detail="Code not found")
            
        if not state:
            raise HTTPException(status_code=400, detail="State parameter missing")
        
        # Get the OAuth session to retrieve the code_verifier
        from app.models.figma_oauth_model import figma_oauth_manager
        
        oauth_session = await figma_oauth_manager.get_oauth_session(state)
        if not oauth_session:
            raise HTTPException(status_code=400, detail="Invalid or expired OAuth session")
        
        # Mark session as used
        await figma_oauth_manager.mark_session_used(state)
        
        # Exchange the code for an access token with PKCE
        code_verifier = oauth_session.get("code_verifier")
        token_response = exchange_figma_code_for_token(code, code_verifier)
        
        if not token_response or not token_response.get("access_token"):
            raise HTTPException(status_code=400, detail="Failed to exchange code for access token. Please check your Figma OAuth configuration.")
        
        access_token = token_response["access_token"]
        print(f"Access token obtained successfully")
        
        # Get the user information from Figma
        user_info = get_figma_user_info(access_token)
        print(f"User info obtained: {user_info is not None}")
        
        if not user_info:
            raise HTTPException(status_code=400, detail="Unable to fetch user info from Figma. The access token may be invalid.")
        
        # Save credentials using the new OAuth manager
        from app.models.figma_oauth_model import FigmaOAuthCredentials
        
        credentials = FigmaOAuthCredentials(
            tenant_id=tenant_id,
            user_id=userId,
            figma_user_id=str(user_info["id"]),
            figma_email=user_info.get("email", ""),
            figma_name=user_info.get("handle", user_info.get("email", "")),
            access_token=access_token,
            refresh_token=token_response.get("refresh_token"),
            expires_in=token_response.get("expires_in")
        )
        
        saved = await figma_oauth_manager.save_credentials(credentials)
        if not saved:
            raise HTTPException(status_code=500, detail="Failed to save OAuth credentials")
        
        # Also maintain backward compatibility with existing collection
        status = "true"
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='users_figma'
        )
        
        user_data = {
            "figma_id": user_info["id"],
            "username": user_info.get("handle", user_info.get("email")),
            "userId": userId,
            "tenant_id": tenant_id,
            "status": status,
            "email": user_info.get("email"),
            "name": user_info.get("handle", ""),
            "access_token": access_token,
            "img_url": user_info.get("img_url"),
            "profile_url": f"https://www.figma.com/@{user_info.get('handle', '')}",
            "updated_at": datetime.utcnow(),
        }
        
        existing_user = await mongo_handler.get_one({"figma_id": user_info["id"]}, mongo_handler.db)
        if existing_user:
            update_result = await mongo_handler.update_one(
                {"figma_id": user_info["id"]},
                user_data,
                db=mongo_handler.db
            )
            if update_result:
                print(f"Figma user updated: {user_data['username']}")
            else:
                raise HTTPException(status_code=500, detail="Failed to update user in database")
        else:
            user_data["created_at"] = datetime.utcnow()
            mongo_result = await mongo_handler.insert(user_data, mongo_handler.db)
            if not mongo_result:
                raise HTTPException(status_code=500, detail="Failed to create user in database")
            print(f"Figma user created: {user_data['username']}")
        
        return {"Message": f"You can close this window - {userId, status}"}
        
    except Exception as e:
        print(f"Error in Figma OAuth callback: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error during OAuth callback: {str(e)}")

@router.delete("/oauth/disconnect")
async def figma_oauth_disconnect(current_user=Depends(get_current_user)):
    """
    Disconnect Figma OAuth connection for current user.
    Following the same pattern as GitHub OAuth.
    """
    try:
        tenant_id = get_tenant_id()
        user_id = current_user.get("sub")
        
        # Delete from legacy collection
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='users_figma'
        )
        legacy_deleted = await mongo_handler.delete_by_filter({
            "userId": user_id,
            "tenant_id": tenant_id
        }, mongo_handler.db)
        
        # Delete from new OAuth credentials collection
        from app.models.figma_oauth_model import figma_oauth_manager
        oauth_deleted = await figma_oauth_manager.delete_credentials(tenant_id, user_id)
        
        print(f"Disconnect debug - tenant_id: {tenant_id}, user_id: {user_id}")
        print(f"Legacy deleted count: {legacy_deleted.deleted_count}")
        print(f"OAuth deleted: {oauth_deleted}")
        
        if legacy_deleted.deleted_count > 0 or oauth_deleted:
            return JSONResponse(
                status_code=200,
                content={"message": "Figma OAuth connection disconnected successfully"}
            )
        else:
            return JSONResponse(
                status_code=404,
                content={"message": "No OAuth connection found to disconnect"}
            )
            
    except Exception as e:
        status_code = classify_exception_status_code(e)
        return JSONResponse(
            status_code=status_code,
            content={"message": f"Failed to disconnect OAuth: {str(e)}"}
        )


@router.get("/oauth/figma_connected_status")
async def figma_connected_status(
    user_id: str = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Check if a user is connected to Figma by their user_id.
    Only checks OAuth credentials collection.
    """
    if not user_id:
        user_id = current_user.get("sub")
    
    tenant_id = get_tenant_id()
    
    try:
        # Check only OAuth credentials collection
        from app.models.figma_oauth_model import figma_oauth_manager
        
        oauth_credentials = await figma_oauth_manager.get_credentials(tenant_id, user_id)
        
        print(f"Figma OAuth connection check for user {user_id}:")
        print(f"  OAuth credentials: {oauth_credentials is not None}")
        
        if not oauth_credentials:
            return JSONResponse(
                status_code=200,
                content={
                    "figma_connected": False, 
                    "message": "Figma user not connected via OAuth",
                    "connection_type": None
                }
            )
        
        # Return OAuth connection details
        response_data = {
            "figma_connected": True,
            "message": "Figma user connected via OAuth",
            "status": "active" if oauth_credentials.get("is_active", True) else "inactive",
            "username": oauth_credentials.get("figma_name"),
            "figma_user_id": oauth_credentials.get("figma_user_id"),
            "email": oauth_credentials.get("figma_email"),
            "name": oauth_credentials.get("figma_name"),
            "created_at": oauth_credentials.get("created_at"),
            "updated_at": oauth_credentials.get("updated_at"),
        }
        
        return response_data

    except Exception as e:
        print(f"Error checking Figma OAuth connection status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error accessing database: {str(e)}"
        )

# Complete Figma node type coverage for real-world usage
DIRECT_VISUAL_TYPES = frozenset([
    'IMAGE', 'COMPONENT', 'INSTANCE', 'COMPONENT_SET', 'MEDIA'
])

VECTOR_GRAPHICS_TYPES = frozenset([
    'VECTOR', 'BOOLEAN_OPERATION', 'STAR', 'POLYGON', 'LINE', 'ELLIPSE',
    'REGULAR_POLYGON', 'CONNECTOR', 'STAMP'
])

SHAPE_TYPES = frozenset([
    'RECTANGLE', 'ELLIPSE', 'SHAPE_WITH_TEXT'
])

TEXT_TYPES = frozenset([
    'TEXT', 'TEXT_PATH', 'CODE_BLOCK'
])

CONTAINER_TYPES = frozenset([
    'FRAME', 'GROUP', 'SECTION', 'TABLE', 'TABLE_CELL'
])

INTERACTIVE_TYPES = frozenset([
    'STICKY', 'EMBED', 'LINK_UNFURL', 'WIDGET'
])

UTILITY_TYPES = frozenset([
    'SLICE', 'TRANSFORM_GROUP'
])

# All visual asset types (excluding structural nodes like DOCUMENT, PAGE, CANVAS)
ALL_VISUAL_TYPES = (DIRECT_VISUAL_TYPES | VECTOR_GRAPHICS_TYPES | SHAPE_TYPES |
                   TEXT_TYPES | CONTAINER_TYPES | INTERACTIVE_TYPES | UTILITY_TYPES)

# Priority for selecting primary node (lower number = higher priority)
TYPE_PRIORITY = {
    # Direct visual content (highest priority)
    "IMAGE": 0, "MEDIA": 1, "VECTOR": 2, "COMPONENT": 3, "INSTANCE": 4,

    # Shapes and graphics
    "RECTANGLE": 5, "ELLIPSE": 6, "POLYGON": 7, "STAR": 8, "BOOLEAN_OPERATION": 9,
    "LINE": 10, "CONNECTOR": 11, "STAMP": 12, "SHAPE_WITH_TEXT": 13,

    # Text elements
    "TEXT": 14, "TEXT_PATH": 15, "CODE_BLOCK": 16,

    # Interactive elements
    "STICKY": 17, "EMBED": 18, "LINK_UNFURL": 19, "WIDGET": 20,

    # Containers and structure
    "COMPONENT_SET": 21, "FRAME": 22, "GROUP": 23, "SECTION": 24,
    "TABLE": 25, "TABLE_CELL": 26,

    # Utility types (lowest priority)
    "SLICE": 27, "TRANSFORM_GROUP": 28
}

def _get_node_dimensions(node: dict) -> tuple[float, float]:
    """Extract width and height from node."""
    bbox = node.get('absoluteBoundingBox', {})
    dimensions = node.get('dimensions', {})
    width = bbox.get('width', 0) or dimensions.get('width', 0)
    height = bbox.get('height', 0) or dimensions.get('height', 0)
    return width, height

def _check_naming_conventions(node_name: str) -> tuple[bool, str]:
    """
    Check if node follows explicit naming conventions for icons and images.
    
    Patterns:
    - Icons: "ic/icon_name" (e.g., "ic/home", "ic/user_profile")
    - Images: "img/image_name" (e.g., "img/hero_banner", "img/profile_pic")
    
    Returns:
        tuple[bool, str]: (matches_convention, asset_type)
    """
    import re
    
    # Exact patterns as suggested
    icon_pattern = re.compile(r'^(ic)/([a-z0-9_]+)$')
    image_pattern = re.compile(r'^(img)/([a-z0-9_]+)$')
    
    if icon_pattern.match(node_name):
        return True, 'icon'
    if image_pattern.match(node_name):
        return True, 'image'
    
    return False, ''

def _is_visual_asset(node: dict) -> tuple[bool, str]:
    """
    Generic visual asset detection for user-submitted Figma files.
    Detects icons and images based on Figma properties, not naming conventions.
    Balances comprehensive detection with noise reduction.

    Returns:
        tuple[bool, str]: (is_visual_asset, asset_type)
    """
    node_type = node.get('figma_type', node.get('type', '')).upper()
    node_name = (node.get('name', '') or '').lower().strip()
    fills = node.get('fills', [])
    width, height = _get_node_dimensions(node)

    # HIGHEST PRIORITY: Explicit naming conventions (when available)
    follows_convention, convention_type = _check_naming_conventions(node_name)
    if follows_convention:
        return True, convention_type

    # 1. Direct IMAGE nodes and IMAGE fills (guaranteed images)
    if node_type == 'IMAGE':
        return True, 'image'
    
    for fill in fills:
        if isinstance(fill, dict) and fill.get('type') == 'IMAGE' and fill.get('visible', True):
            return True, 'image'

    # 2. Nodes with existing image URLs
    if node.get('imageUrl'):
        return True, 'image'

    # 3. MEDIA nodes
    if node_type == 'MEDIA':
        return True, 'media'

    # 4. Export settings (user-marked for export)
    if node.get('exportSettings'):
        # Classify as icon or image based on size
        if width <= 64 and height <= 64:
            return True, 'icon'
        return True, 'export'

    # 5. Components and Instances (common for icons/images)
    if node_type in {'COMPONENT', 'INSTANCE', 'COMPONENT_SET'}:
        # Small components are likely icons
        if width <= 64 and height <= 64:
            return True, 'icon'
        # Medium components with visual keywords
        elif width <= 400 and height <= 400:
            if any(keyword in node_name for keyword in ['icon', 'image', 'img', 'logo', 'avatar']):
                return True, 'component'

    # 6. Vector graphics (common for icons)
    if node_type in {'VECTOR', 'BOOLEAN_OPERATION', 'STAR', 'POLYGON', 'ELLIPSE', 'REGULAR_POLYGON'}:
        # Small vectors without children are likely icons
        is_icon_sized = 8 <= width <= 64 and 8 <= height <= 64
        has_no_children = not node.get('children')
        
        if is_icon_sized and has_no_children:
            return True, 'icon'

    # 7. Small rectangles with fills (could be icons or image placeholders)
    if node_type == 'RECTANGLE':
        has_no_children = not node.get('children')
        has_fills = any(isinstance(f, dict) and f.get('visible', True) for f in fills)
        
        # Small filled rectangles without children
        if has_fills and has_no_children:
            if width <= 64 and height <= 64:
                return True, 'icon'
            elif width <= 400 and height <= 400:
                # Only if named like an image
                if any(keyword in node_name for keyword in ['image', 'img', 'photo', 'picture']):
                    return True, 'image'

    return False, ''

def _create_signature(node: dict) -> str:
    """
    Create comprehensive deduplication signature for Figma nodes.

    Generates unique signatures based on node type, name, dimensions, and content
    to prevent duplicate processing of similar visual assets.
    """
    node_type = node.get('figma_type', node.get('type', '')).lower()
    name = (node.get('name', '') or '').lower().strip()
    width, height = _get_node_dimensions(node)

    # Convert dimensions to integers for signature
    w, h = int(width), int(height)

    # Priority-based signature generation

    # 1. Named images (highest specificity)
    if 'image' in name:
        return f"image|{name}"

    # 2. Components (by name for reusability)
    if node_type in ('component', 'instance', 'component_set'):
        return f"component|{name}"

    # 3. Media elements (by name)
    if node_type in ('media', 'image'):
        return f"media|{name}"

    # 4. Text elements (by content hash for uniqueness)
    if node_type in ('text', 'text_path', 'code_block'):
        text_content = (node.get('characters', '') or '')[:50]
        if text_content:
            content_hash = hash(text_content) % 10000
            return f"text|{name}|{content_hash}"
        return f"text|{name}"

    # 5. Vector graphics (by type and dimensions)
    if node_type in ('vector', 'boolean_operation', 'star', 'polygon', 'line',
                     'ellipse', 'regular_polygon', 'connector', 'stamp'):
        return f"vector|{node_type}|{w}x{h}"

    # 6. Shapes (by type and dimensions)
    if node_type in ('rectangle', 'shape_with_text'):
        return f"shape|{node_type}|{w}x{h}"

    # 7. Interactive elements (by name and type)
    if node_type in ('sticky', 'embed', 'link_unfurl', 'widget'):
        return f"interactive|{node_type}|{name}"

    # 8. Table elements (by position and content)
    if node_type in ('table', 'table_cell'):
        return f"table|{node_type}|{name}|{w}x{h}"

    # 9. Default fallback
    return f"{node_type}|{w}x{h}"

async def get_all_image_nodes(node: dict) -> List[dict]:
    """Extract visual asset nodes with optimized traversal"""
    image_nodes = []
    added_ids = set()

    def extract_node_data(node: dict) -> dict:
        """Extract essential node data"""
        return {
            "id": node.get("id", ""),
            "name": node.get("name", "Unknown"),
            "type": node.get('figma_type', node.get('type', 'Unknown')),
            "absoluteBoundingBox": node.get('absoluteBoundingBox'),
            "dimensions": node.get('dimensions'),
            "imageUrl": node.get('imageUrl')
        }

    def traverse(current_node: dict):
        """Optimized traversal with early returns"""
        try:
            is_asset, asset_type = _is_visual_asset(current_node)

            if is_asset:
                node_id = current_node.get("id", "")
                if node_id and node_id not in added_ids:
                    extracted_node = extract_node_data(current_node)
                    extracted_node.update({
                        "asset_category": asset_type,
                        "signature": _create_signature(current_node)
                    })
                    image_nodes.append(extracted_node)
                    added_ids.add(node_id)

            # Traverse children
            for child in current_node.get("children", []):
                traverse(child)

        except Exception:
            pass  # Skip problematic nodes

    traverse(node)
    return image_nodes



def _deduplicate_by_signature(nodes: List[dict]) -> List[dict]:
    """Remove duplicates using visual signatures"""
    seen_signatures = set()
    unique_nodes = []

    for node in nodes:
        signature = node.get('signature', '')
        if signature and signature not in seen_signatures:
            seen_signatures.add(signature)
            unique_nodes.append(node)
        elif not signature:
            unique_nodes.append(node)

    return unique_nodes

async def get_figma_image_url_oauth(frame_ids: Union[str, List[str]], access_token: str, file_key: str) -> Dict[str, str]:
    """Fetch Figma image URLs using OAuth token"""
    if isinstance(frame_ids, str):
        frame_ids = [frame_ids]

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(
                f"https://api.figma.com/v1/images/{file_key}",
                params={"ids": ",".join(frame_ids), "scale": 2, "format": "png"},
                headers={"Authorization": f"Bearer {access_token}"}
            )

            if response.status_code == 200:
                images = response.json().get('images', {})
                return {fid: images.get(fid, "") for fid in frame_ids if images.get(fid)}
            return {}
    except Exception:
        return {}

async def fetch_icons_as_svg(node_ids: List[str], file_key: str, figma_api_key: str = None, access_token: str = None) -> Dict[str, str]:
    """Fetch icons in SVG format for better quality and smaller file size"""
    if not node_ids:
        return {}
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            url = f"https://api.figma.com/v1/images/{file_key}"
            params = {
                "ids": ",".join(node_ids),
                "format": "svg"  # SVG format for icons
            }
            
            if access_token:
                headers = {"Authorization": f"Bearer {access_token}"}
            else:
                headers = {"X-Figma-Token": figma_api_key}
            
            response = await client.get(url, params=params, headers=headers)
            
            if response.status_code == 200:
                images = response.json().get('images', {})
                return {nid: images.get(nid, "") for nid in node_ids if images.get(nid)}
            return {}
    except Exception as e:
        print(f"Error fetching SVG icons: {e}")
        return {}

async def batch_fetch_images(image_nodes: List[dict], file_key: str, figma_api_key: str = None, access_token: str = None) -> Dict[str, str]:
    """Fetch images in optimized batches with format optimization (SVG for icons, PNG for images)"""
    if not image_nodes:
        return {}

    # Separate icons from images based on asset_category
    icon_nodes = [n for n in image_nodes if n.get('asset_category') == 'icon']
    image_nodes_only = [n for n in image_nodes if n.get('asset_category') != 'icon']

    all_image_urls = {}
    fetched_ids = set()  # Track which IDs have been fetched to prevent duplicates

    # Fetch icons as SVG ONLY (better quality, smaller size)
    if icon_nodes:
        icon_ids = [n['id'] for n in icon_nodes]
        print(f"🎨 Fetching {len(icon_ids)} icons in SVG format ONLY...")
        icon_urls = await fetch_icons_as_svg(icon_ids, file_key, figma_api_key, access_token)
        all_image_urls.update(icon_urls)
        fetched_ids.update(icon_ids)  # Mark these as fetched
        print(f"✅ Fetched {len(icon_urls)} SVG icons")

    # Fetch images as PNG (standard format) - SKIP any already fetched as SVG
    if image_nodes_only:
        # Filter out any nodes already fetched as icons
        image_nodes_to_fetch = [n for n in image_nodes_only if n['id'] not in fetched_ids]
        
        if image_nodes_to_fetch:
            print(f"🖼️  Fetching {len(image_nodes_to_fetch)} images in PNG format...")
            
            # Dynamic batch sizing
            total_nodes = len(image_nodes_to_fetch)
            batch_size = min(50, max(10, total_nodes // 4)) if total_nodes > 20 else 10

            # Override from environment
            try:
                env_batch_size = int(os.getenv("FIGMA_BATCH_SIZE", "0"))
                if env_batch_size > 0:
                    batch_size = env_batch_size
            except ValueError:
                pass

            semaphore = asyncio.Semaphore(int(os.getenv("FIGMA_MAX_CONCURRENT_BATCHES", "3")))

            async def process_batch(batch_nodes: List[dict]) -> Dict[str, str]:
                async with semaphore:
                    batch_ids = [node['id'] for node in batch_nodes]

                    try:
                        if access_token:
                            return await get_figma_image_url_oauth(batch_ids, access_token, file_key)
                        else:
                            return await get_figma_image_url(batch_ids, figma_api_key, file_key)
                    except Exception:
                        return {}

            # Create and execute batch tasks
            batches = [image_nodes_to_fetch[i:i + batch_size] for i in range(0, len(image_nodes_to_fetch), batch_size)]
            tasks = [process_batch(batch) for batch in batches]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Combine results
            for result in results:
                if isinstance(result, dict):
                    all_image_urls.update(result)
            
            print(f"✅ Fetched {len(all_image_urls) - len(icon_urls) if icon_nodes else len(all_image_urls)} PNG images")
        else:
            print(f"⏭️  Skipped {len(image_nodes_only)} images (already fetched as SVG icons)")

    return all_image_urls



def _pick_primary_node(nodes: List[dict]) -> dict:
    """Select primary node using type priority"""
    def get_rank(node: dict) -> tuple:
        node_type = node.get("type", "")
        priority = TYPE_PRIORITY.get(node_type, 999)
        name_len = len(node.get("name", ""))
        node_id = node.get("id", "")
        return (priority, name_len, node_id)

    return min(nodes, key=get_rank)

def _get_file_extension(content_type: str) -> str:
    """Get file extension from content type"""
    content_type = (content_type or "").lower()
    if "svg" in content_type:
        return ".svg"
    elif "jpeg" in content_type or "jpg" in content_type:
        return ".jpg"
    elif "webp" in content_type:
        return ".webp"
    return ".png"

def _safe_filename(node_id: str) -> str:
    """Create safe filename from node ID"""
    if not node_id:
        return "node"
    safe_id = node_id.replace(":", "_").replace(";", "_")
    return safe_id[1:] if safe_id.startswith("I") else safe_id

async def process_and_save_images(data: dict, file_key: str, output_directory: str,
                                figma_api_key: str = None, access_token: str = None) -> Tuple[List[dict], Dict[str, str], Dict[str, str]]:
    """Process and save SOLO images and icons only - no background noise"""
    try:
        import hashlib
        os.makedirs(output_directory, exist_ok=True)

        # Extract SOLO images and icons only (strict filtering)
        image_nodes = await get_all_image_nodes(data.get('document', {}))
        filtered_nodes = _deduplicate_by_signature(image_nodes)

        print(f"🎯 SOLO EXTRACTION: Found {len(image_nodes)} raw nodes, {len(filtered_nodes)} after deduplication")

        # Fetch image URLs
        image_urls = await batch_fetch_images(filtered_nodes, file_key, figma_api_key, access_token)

        # Group nodes by URL
        node_by_id = {n["id"]: n for n in filtered_nodes if n.get("id")}
        url_groups = {}
        for node_id, url in image_urls.items():
            if url and node_id in node_by_id:
                url_groups.setdefault(url, []).append(node_by_id[node_id])

        # Process downloads
        saved_images = []
        url_to_path_mapping = {}
        md5_to_path = {}

        semaphore = asyncio.Semaphore(int(os.getenv("FIGMA_MAX_CONCURRENT_DOWNLOADS", "8")))

        async def download_and_save(url: str, nodes: List[dict]):
            async with semaphore:
                try:
                    primary = _pick_primary_node(nodes)

                    async with httpx.AsyncClient(timeout=30.0) as client:
                        response = await client.get(url)
                        if response.status_code != 200 or not response.content:
                            return

                        content = response.content
                        md5_hash = hashlib.md5(content).hexdigest()

                        # Check if already saved by MD5
                        if md5_hash in md5_to_path:
                            final_path = md5_to_path[md5_hash]
                        else:
                            # Create new file
                            ext = _get_file_extension(response.headers.get("content-type"))
                            safe_id = _safe_filename(primary.get("id", ""))
                            filename = f"figma_image_{safe_id}{ext}"
                            final_path = os.path.join(output_directory, filename)

                            # Handle filename conflicts
                            if os.path.exists(final_path):
                                filename = f"figma_image_{safe_id}_{md5_hash[:8]}{ext}"
                                final_path = os.path.join(output_directory, filename)

                            # Save file
                            with open(final_path, "wb") as f:
                                f.write(content)

                            md5_to_path[md5_hash] = final_path

                        # Update mappings
                        relative_path = f"/assets/figmaimages/{os.path.basename(final_path)}"
                        url_to_path_mapping[url] = relative_path

                        saved_images.append({
                            "id": primary.get("id", ""),
                            "name": primary.get("name", "Unknown"),
                            "type": primary.get("type", "UNKNOWN"),
                            "url": url,
                            "local_path": final_path,
                            "relative_path": relative_path,
                            "is_duplicate": False
                        })

                except Exception as e:
                    print(f"Error downloading {url}: {e}")

        # Execute downloads
        tasks = [download_and_save(url, nodes) for url, nodes in url_groups.items()]
        await asyncio.gather(*tasks, return_exceptions=True)

        return saved_images, url_to_path_mapping, image_urls

    except Exception as e:
        print(f"Error processing images: {e}")
        return [], {}, {}

# Add this Pydantic model for the request
class FetchAssetScreenRequest(BaseModel):
    filename: str
    screen_name: Optional[str] = None
    task_id: str
    agent_name: str = "CodeGeneration"
    tenant_id: Optional[str] = None
    project_id: Optional[str] = None

# Add helper function to find HTML files with fallbacks
def find_html_file_with_fallbacks(filename: str, assets_dir: str):
    """
    Enhanced fallback logic to find HTML files with multiple naming patterns in assets directory only.

    For screen with ID, checks in this order:
    1. Primary filename: "todo-page-1-10.html"
    2. Screen ID only: "1-10.html"
    3. Any file ending with screen ID: "*1-10.html" (for LLM variations)

    Args:
        filename (str): The primary filename to look for
        assets_dir (str): Assets directory path where HTML files are generated

    Returns:
        tuple: (html_path, assets_dir) if found, None otherwise
    """
    try:
        import glob
        import re

        if not os.path.exists(assets_dir):
            print(generate_timestamp(), f"Assets directory doesn't exist: {assets_dir}")
            return None

        # Extract screen ID from filename if present (e.g., "todo-page-1-10.html" -> "1-10")
        screen_id_match = re.search(r'-(\d+(?:-\d+)+)\.html$', filename)
        screen_id_part = screen_id_match.group(1) if screen_id_match else None

        print(generate_timestamp(), f"Searching for HTML file with fallbacks in assets directory: {assets_dir}")
        print(generate_timestamp(), f"Primary filename: {filename}")
        if screen_id_part:
            print(generate_timestamp(), f"Extracted screen ID part: {screen_id_part}")

        # Define search patterns in priority order
        search_patterns = []

        # Pattern 1: Original filename
        search_patterns.append(filename)

        # Pattern 2: Screen ID only (if available)
        if screen_id_part:
            screen_id_filename = f"{screen_id_part}.html"
            search_patterns.append(screen_id_filename)
            print(generate_timestamp(), f"Will also check screen ID only: {screen_id_filename}")

        # Check exact filename patterns first
        for pattern in search_patterns:
            test_path = os.path.join(assets_dir, pattern)
            if os.path.exists(test_path):
                print(generate_timestamp(), f"Found exact match: {pattern}")
                return (test_path, assets_dir)

        # Additional pattern: Check for any HTML files with the screen ID (only if screen_id exists)
        if screen_id_part:
            screen_id_glob = os.path.join(assets_dir, f"*{screen_id_part}.html")
            id_matches = glob.glob(screen_id_glob)
            if id_matches:
                id_matches.sort(key=lambda p: os.path.getmtime(p), reverse=True)
                found_path = id_matches[0]
                print(generate_timestamp(), f"Found screen ID glob match: {os.path.basename(found_path)}")
                return (found_path, assets_dir)

        print(generate_timestamp(), f"No HTML file found with any fallback pattern")
        return None

    except Exception as e:
        print(generate_timestamp(), f"Error in fallback HTML file search: {e}")
        return None

# Add the main processing function
def process_html_with_assets(filename: str, task_id: str, agent_name: str = "CodeGeneration", tenant_id: str = None, project_id: str = None):
    """
    Process HTML file and inline CSS/JS assets and convert images to base64

    Args:
        filename (str): The HTML filename to process
        task_id (str): The task ID to locate the assets directory
        agent_name (str): The agent name for path resolution

    Returns:
        str: HTML content with inlined CSS, JS, and base64 images, or None if error
    """
    try:
        print("*******************************************************")

        # Resolve paths
        if tenant_id and project_id:
            # Use the new path format when tenant_id and project_id are provided
            base_path = "/tmp" if os.environ.get("LOCAL_DEBUG") else "/app/data"
            print(f"base_path: {base_path}")
            assets_dir = f"{base_path}/{tenant_id}/{project_id}/workspace/assets/"
            print(f"assets_dir: {assets_dir}")
        else:
            # Fallback to original logic
            base_dir = get_codegeneration_path(agent_name, task_id)
            print(f"else base_dir: {base_dir}")
            assets_dir = os.path.join(base_dir, "assets")
            print(f"else assets_dir: {assets_dir}")
        html_path = os.path.join(assets_dir, filename)
        print(f"html_path: {html_path}")


        print(generate_timestamp(), f"Processing HTML file: {html_path}")
        print(generate_timestamp(), f"Assets directory: {assets_dir}")

        # Check if HTML file exists
        if not os.path.exists(html_path):
            print(generate_timestamp(), f"HTML file not found: {html_path}")

            # Enhanced fallback logic for multiple filename patterns
            found_file = find_html_file_with_fallbacks(filename, assets_dir)

            if found_file:
                html_path, assets_dir = found_file
                print(generate_timestamp(), f"Found HTML file using fallback logic: {os.path.basename(html_path)}")
            else:
                return None

        # Read HTML content
        with open(html_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        print("************************HTML***************")
        print(generate_timestamp(), f"Read HTML file: {filename} ({len(html_content)} chars)")

        # Process CSS files
        css_pattern = r'<link[^>]*href=["\']([^"\']*\.css)["\'][^>]*>'
        css_matches = re.findall(css_pattern, html_content, re.IGNORECASE)

        print("***********************CSS****************")
        for css_file in css_matches:
            css_path = os.path.join(assets_dir, css_file)
            if os.path.exists(css_path):
                print(generate_timestamp(), f"Inlining CSS file: {css_file}")
                with open(css_path, 'r', encoding='utf-8') as f:
                    css_content = f.read()

                # Replace link tag with inline style
                link_tag_pattern = r'<link[^>]*href=["\']' + re.escape(css_file) + r'["\'][^>]*>'
                inline_style = f'<style>\n{css_content}\n</style>'
                html_content = re.sub(link_tag_pattern, inline_style, html_content, flags=re.IGNORECASE)
            else:
                print(generate_timestamp(), f"CSS file not found: {css_path}")

        # Process JS files
        js_pattern = r'<script[^>]*src=["\']([^"\']*\.js)["\'][^>]*></script>'
        js_matches = re.findall(js_pattern, html_content, re.IGNORECASE)

        print("***********************JS****************")
        for js_file in js_matches:
            js_path = os.path.join(assets_dir, js_file)
            if os.path.exists(js_path):
                print(generate_timestamp(), f"Inlining JS file: {js_file}")
                with open(js_path, 'r', encoding='utf-8') as f:
                    js_content = f.read()

                # Replace script tag with inline script
                script_tag_pattern = r'<script[^>]*src=["\']' + re.escape(js_file) + r'["\'][^>]*></script>'
                inline_script = f'<script>\n{js_content}\n</script>'
                html_content = re.sub(script_tag_pattern, inline_script, html_content, flags=re.IGNORECASE)
            else:
                print(generate_timestamp(), f"JS file not found: {js_path}")

        # Process images in parallel for better performance
        print("***********************IMAGES****************")
        img_pattern = r'<img[^>]*src=["\']([^"\']*)["\'][^>]*>'
        img_matches = re.findall(img_pattern, html_content, re.IGNORECASE)
        
        if img_matches:
            print(generate_timestamp(), f"Found {len(img_matches)} images to process in parallel")
            
            import concurrent.futures
            import threading
            
            def resolve_image_path(img_src, base_assets_dir):
                """
                Dynamically resolve image path from various possible formats
                Handles: figmaimages/, /assets/figmaimages/, assets/figmaimages/, /figmaimages/, ./figmaimages/, filename.ext
                """
                # Normalize the path by removing leading/trailing whitespace and multiple slashes
                img_src = img_src.strip().replace('//', '/')
                
                # Define possible path patterns to try, in order of preference
                path_candidates = []
                
                # Pattern 1: Direct relative path from assets_dir (most common)
                if img_src.startswith('/'):
                    # Remove leading slash and try as relative to assets_dir
                    clean_path = img_src.lstrip('/')
                    if clean_path.startswith('assets/'):
                        # Remove 'assets/' prefix since we're already in assets_dir
                        clean_path = clean_path[7:]
                    path_candidates.append(clean_path)
                else:
                    # Already relative, use as-is
                    path_candidates.append(img_src)
                
                # Pattern 2: If it doesn't contain figmaimages, try adding it
                if 'figmaimages' not in img_src:
                    filename = os.path.basename(img_src)
                    path_candidates.append(f"figmaimages/{filename}")
                
                # Pattern 3: Try without any prefixes (direct in assets)
                filename_only = os.path.basename(img_src)
                if filename_only != img_src:  # Only add if it's different
                    path_candidates.append(filename_only)
                
                # Pattern 4: Try common variations
                clean_src = img_src.lstrip('./').lstrip('/')
                if clean_src.startswith('assets/'):
                    clean_src = clean_src[7:]  # Remove 'assets/' prefix
                if clean_src != img_src and clean_src not in path_candidates:
                    path_candidates.append(clean_src)
                
                return path_candidates
            
            def process_single_image(img_src):
                """Process a single image and return replacement data"""
                try:
                    print(generate_timestamp(), f"Processing image: {img_src}")
                    
                    # Get all possible path candidates
                    path_candidates = resolve_image_path(img_src, assets_dir)
                    
                    # Try each candidate until we find an existing file
                    image_path = None
                    for candidate in path_candidates:
                        test_path = os.path.join(assets_dir, candidate)
                        print(generate_timestamp(), f"Trying path: {test_path}")
                        
                        if os.path.exists(test_path) and os.path.isfile(test_path):
                            image_path = test_path
                            print(generate_timestamp(), f"Found image at: {image_path}")
                            break
                    
                    if not image_path:
                        print(generate_timestamp(), f"Image not found after trying {len(path_candidates)} candidates:")
                        for i, candidate in enumerate(path_candidates, 1):
                            test_path = os.path.join(assets_dir, candidate)
                            print(generate_timestamp(), f"  {i}. {test_path} - {'EXISTS' if os.path.exists(test_path) else 'NOT FOUND'}")
                        return None

                    print(generate_timestamp(), f"Converting image to base64: {img_src}")
                    
                    # Read image file as binary
                    with open(image_path, 'rb') as img_file:
                        img_data = img_file.read()
                    
                    # Get MIME type
                    mime_type, _ = mimetypes.guess_type(image_path)
                    if not mime_type:
                        # Default to common image types based on extension
                        ext = os.path.splitext(image_path)[1].lower()
                        if ext == '.png':
                            mime_type = 'image/png'
                        elif ext in ['.jpg', '.jpeg']:
                            mime_type = 'image/jpeg'
                        elif ext == '.gif':
                            mime_type = 'image/gif'
                        elif ext == '.svg':
                            mime_type = 'image/svg+xml'
                        else:
                            mime_type = 'image/png'  # Default fallback
                    
                    # Convert to base64
                    img_base64 = base64.b64encode(img_data).decode('utf-8')
                    data_url = f"data:{mime_type};base64,{img_base64}"
                    
                    print(generate_timestamp(), f"Successfully converted {img_src} to base64 data URL")
                    
                    return {
                        'original_src': img_src,
                        'data_url': data_url
                    }
                    
                except Exception as img_error:
                    print(generate_timestamp(), f"Error processing image {img_src}: {img_error}")
                    return None

            # Process all images concurrently using ThreadPoolExecutor
            start_time = time.time()
            with concurrent.futures.ThreadPoolExecutor(max_workers=min(len(img_matches), 10)) as executor:
                # Submit all tasks
                future_to_img = {executor.submit(process_single_image, img_src): img_src for img_src in img_matches}
                
                # Collect results
                image_results = []
                for future in concurrent.futures.as_completed(future_to_img):
                    result = future.result()
                    if result:
                        image_results.append(result)
            
            processing_time = time.time() - start_time
            print(generate_timestamp(), f"Processed {len(image_results)} images in {processing_time:.2f} seconds using parallel processing")
            
            # Apply all replacements at once
            for result in image_results:
                old_src_pattern = re.escape(result['original_src'])
                html_content = re.sub(
                    rf'(<img[^>]*src=["\']){old_src_pattern}(["\'][^>]*>)',
                    rf'\1{result["data_url"]}\2',
                    html_content,
                    flags=re.IGNORECASE
                )
            
            print(generate_timestamp(), f"Successfully applied {len(image_results)} image replacements")
        else:
            print(generate_timestamp(), "No images found to process")
        print(generate_timestamp(), f"Asset inlining complete for {filename}")
        return html_content

    except Exception as e:
        print(generate_timestamp(), f"Error processing HTML with assets: {e}")
        import traceback
        print(generate_timestamp(), f"Full traceback: {traceback.format_exc()}")
        return None

# Add the new endpoint at the end of the file
@router.post("/fetch_asset_screen")
async def fetch_asset_screen(
    request: FetchAssetScreenRequest,
    current_user=Depends(get_current_user),
):
    """
    Fetch and process HTML asset screen with inlined CSS, JS, and images
    
    Args:
        request: FetchAssetScreenRequest containing filename, screen_name, task_id, and agent_name
        
    Returns:
        JSON response with processed HTML content or error information
    """
    try:
        filename = request.filename
        screen_name = request.screen_name
        task_id = request.task_id
        agent_name = request.agent_name
        tenant_id = request.tenant_id or get_tenant_id()
        project_id = request.project_id

        if not filename:
            raise HTTPException(
                status_code=400,
                detail="Missing filename parameter"
            )

        if not task_id:
            raise HTTPException(
                status_code=400,
                detail="Missing task_id parameter"
            )

        print("*****Calling process_html_with_assets****")
        # Process the HTML file with inlined assets
        html_content = process_html_with_assets(filename, task_id, agent_name, tenant_id, project_id)
        if html_content:
            return JSONResponse(
                status_code=200,
                content={
                    'filename': filename,
                    'html_content': html_content,
                    'status': 'success',
                    'show_play_icon': False
                }
            )
        else:
            # HTML file doesn't exist, return flag to show play icon
            return JSONResponse(
                status_code=200,
                content={
                    'filename': filename,
                    'status': 'no_html',
                    'show_play_icon': True,
                    'screen_name': screen_name
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        print(generate_timestamp(), f"Error processing fetch_asset_screen: {e}")
        raise HTTPException(
            status_code=500,
            detail=f'Error processing asset screen: {str(e)}'
        )