import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from app.connection.establish_db_connection import get_node_db, get_mongo_db
from app.models.project_steps_model import (
    ProjectStepsStatus,
    StepInfo,
    StepStatus,
    STEP_NODE_MAPPING
)
from app.utils.datetime_utils import generate_timestamp

logger = logging.getLogger(__name__)

class ProjectStepsService:
    """Service for tracking project step completion status"""

    def __init__(self):
        self.node_db = get_node_db()
        self.mongo_db = get_mongo_db()

    async def get_project_steps_status(self, project_id: int) -> ProjectStepsStatus:
        """
        Get the completion status of all project steps

        Args:
            project_id: ID of the project to check

        Returns:
            ProjectStepsStatus object with all step information
        """
        try:
            # Get project details
            project_node = await self.node_db.get_node_by_id(project_id)
            if not project_node:
                raise ValueError(f"Project with ID {project_id} not found")

            project_title = project_node.get('properties', {}).get('Title', 'Unknown Project')
            print('project_node: ', project_node)

            # Get all step statuses
            steps = []
            for step_name, step_config in STEP_NODE_MAPPING.items():
                step_info = await self._get_step_status(project_id, step_name, step_config)
                steps.append(step_info)

            # Calculate overall progress
            completed_steps = sum(1 for step in steps if step.status == StepStatus.COMPLETED)
            overall_progress = (completed_steps / len(steps)) * 100 if steps else 0

            return ProjectStepsStatus(
                project_id=project_id,
                project_title=project_title,
                steps=steps,
                overall_progress=round(overall_progress, 2)
            )

        except Exception as e:
            logger.error(f"Error getting project steps status for project {project_id}: {str(e)}")
            raise

    async def _get_step_status(self, project_id: int, step_name: str, step_config: Dict) -> StepInfo:
        """
        Get the status of a specific step by checking related nodes

        Args:
            project_id: Project ID
            step_name: Name of the step
            step_config: Configuration for the step

        Returns:
            StepInfo object with step status
        """
        try:
            # Check project guidance flow for additional status info
            guidance_flow = await self._get_guidance_flow_status(project_id, step_name)

            # If we have guidance flow info, use it
            if guidance_flow:
                return StepInfo(
                    step_name=step_name,
                    status=StepStatus.COMPLETED if guidance_flow.get('status') == 'completed' else StepStatus.IN_PROGRESS,
                    node_id=guidance_flow.get('node_id'),
                    completed_at=guidance_flow.get('updated_at')
                )

            # No completed nodes found for this step
            return StepInfo(
                step_name=step_name,
                status=StepStatus.NOT_STARTED
            )

        except Exception as e:
            logger.error(f"Error getting status for step {step_name}: {str(e)}")
            # Return not started on error
            return StepInfo(
                step_name=step_name,
                status=StepStatus.NOT_STARTED
            )

    async def _get_guidance_flow_status(self, project_id: int, step_name: str) -> Optional[Dict]:
        """
        Get status information from project guidance flow

        Args:
            project_id: Project ID
            step_name: Name of the step

        Returns:
            Dictionary with guidance flow status or None
        """
        try:
            # Map step names to guidance flow step names
            step_mapping = [
                "document_upload",
                "project_configuration",
                "requirement_configuration",
                "architecture_requirement",
                "system_architecture",
                "container",
                "code_generation"
            ]

            if step_name not in step_mapping:
                return None

            # Get guidance flow from MongoDB
            mongo_handler = get_mongo_db(db_name="kavia_root", collection_name="project_guidance_flow")
            guidance_doc = await mongo_handler.get_one(
                {"project_id": project_id},
                mongo_handler.db
            )

            if not guidance_doc or "steps" not in guidance_doc:
                return None

            steps = guidance_doc["steps"]

            # Check if the step exists in guidance flow
            if step_name in steps:
                step_data = steps[step_name]

                return {
                    "status": step_data.get("status", "pending"),
                    "updated_at": step_data.get("updated_at"),
                    "node_id": step_data.get("node_id")
                }

            # Check for epic-specific or container-specific steps
            if step_name in ["requirement_configuration", "container"]:
                # Check epics for requirement configuration
                if step_name == "requirement_configuration" and "epics" in steps:
                    epics = steps["epics"]
                    completed_epics = sum(1 for epic in epics.values() if epic.get("status") == "completed")
                    total_epics = len(epics)

                    if total_epics > 0:
                        return {
                            "status": "completed" if completed_epics == total_epics else "in_progress",
                            "updated_at": datetime.utcnow().isoformat(),
                            "completed_count": completed_epics,
                            "total_count": total_epics
                        }

                # Check containers for container step
                if step_name == "container" and "containers" in steps:
                    containers = steps["containers"]
                    completed_containers = sum(1 for container in containers.values() if container.get("status") == "completed")
                    total_containers = len(containers)

                    if total_containers > 0:
                        return {
                            "status": "completed" if completed_containers == total_containers else "in_progress",
                            "updated_at": datetime.utcnow().isoformat(),
                            "completed_count": completed_containers,
                            "total_count": total_containers
                        }

            return None

        except Exception as e:
            logger.error(f"Error getting guidance flow status: {str(e)}")
            return None

    async def update_step_status(self, project_id: int, step_name: str, status: StepStatus, node_id: Optional[int] = None) -> bool:
        """
        Update the status of a specific step

        Args:
            project_id: Project ID
            step_name: Name of the step to update
            status: New status for the step
            node_id: Optional node ID associated with the step

        Returns:
            Boolean indicating success
        """
        try:
            # Update project guidance flow
            mongo_handler = get_mongo_db(db_name="kavia_root", collection_name="project_guidance_flow")

            # Get existing document or create new one
            existing_doc = await mongo_handler.get_one(
                {"project_id": project_id},
                mongo_handler.db
            )

            now = datetime.utcnow()

            if existing_doc:
                # Update existing document
                steps = existing_doc.get("steps", {})
                steps[step_name] = {
                    "status": status.value,
                    "updated_at": now.isoformat(),
                    "node_id": node_id
                }

                await mongo_handler.update_one(
                    {"project_id": project_id},
                    {"steps": steps, "updated_at": now.isoformat()},
                    mongo_handler.db
                )
            else:
                # Create new document
                new_doc = {
                    "project_id": project_id,
                    "created_at": now.isoformat(),
                    "updated_at": now.isoformat(),
                    "steps": {
                        step_name: {
                            "status": status.value,
                            "updated_at": now.isoformat(),
                            "node_id": node_id
                        }
                    }
                }

                await mongo_handler.insert(new_doc, mongo_handler.db)

            logger.info(f"Updated step status for project {project_id}, step {step_name} to {status.value}")
            return True

        except Exception as e:
            logger.error(f"Error updating step status: {str(e)}")
            return False

    async def get_step_completion_summary(self, project_id: int) -> Dict[str, Any]:
        """
        Get a summary of step completion for a project

        Args:
            project_id: Project ID

        Returns:
            Dictionary with completion summary
        """
        try:
            steps_status = await self.get_project_steps_status(project_id)

            summary = {
                "project_id": project_id,
                "project_title": steps_status.project_title,
                "overall_progress": steps_status.overall_progress,
                "total_steps": len(steps_status.steps),
                "completed_steps": sum(1 for step in steps_status.steps if step.status == StepStatus.COMPLETED),
                "in_progress_steps": sum(1 for step in steps_status.steps if step.status == StepStatus.IN_PROGRESS),
                "not_started_steps": sum(1 for step in steps_status.steps if step.status == StepStatus.NOT_STARTED),
                "skipped_steps": sum(1 for step in steps_status.steps if step.status == StepStatus.SKIPPED),
                "steps_detail": [
                    {
                        "step_name": step.step_name,
                        "status": step.status.value,
                        "node_id": step.node_id,
                    }
                    for step in steps_status.steps
                ]
            }

            return summary

        except Exception as e:
            logger.error(f"Error getting step completion summary: {str(e)}")
            raise