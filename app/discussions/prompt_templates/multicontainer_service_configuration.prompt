{% extends "base_discussion.prompt" %}

You are an expert software architect configuring a multi-container service system.

{% block task_description_common_preface %}
{% if config_state != "configured" %}
As an expert software architect, configure the System Context based on the C4 model:

{% else %}
You are an expert system architect reviewing the System Context for potential reconfiguration.

1. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - System context: 
     - Users: {{ bg_info.get('system_context', {}).get('users') | tojson(indent=2) }}
     - External Systems: {{ bg_info.get('system_context', {}).get('external_systems') | tojson(indent=2) }}
     - Containers: {{ bg_info.get('system_context', {}).get('containers') | tojson(indent=2) }}
   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}

   - Updated requirements context:
     {% set new_req_context = new_bg.get('requirements_context', {}) %}
   
   - Updated architecture requirements context:
     - Functional Requirements:
       {% for req in new_req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in new_req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

Analysis Instructions:

1. Impact Analysis:
   - Compare original and new project context
   - Compare original and new functional/non-functional requirements
   
2. Container Architecture Analysis:
   - Review existing containers against new requirements
   - Evaluate container responsibilities and boundaries
   - Analyze interface adequacy for new requirements

4. Required Changes:
   - Modify Existing System Context, containers, users, external_systems as needed based on the above analysis
   - Follow the below guidelines as a reference while modifying the existing system_context.
   - IMPORTANT: Set the "changes_needed" flag to true if modifications are done else set to false.

{% endif %}

GENERIC GUIDELINES:

1. Review the project Details, Functional and Non-Functional Requirements and configure the c4 model based System context for this project. 

2. Provide detailed description of the system's purpose and main functionalities.

3. CRITICAL ARCHITECTURAL CONSTRAINTS: You MUST follow these rules strictly:
   - Create MULTIPLE containers as children for the current node
   - Each container should encapsulate a distinct set of functionality
   - Each container must be service-oriented with well-defined boundaries
   - Set the ContainerType as "internal" for each service container
   
4. Multi-Container Service Principles:
   - Each container should have a clearly defined purpose and responsibility
   - Each container should contain EXACTLY ONE component by default that implements all container functionality
   - Functionality should be distributed across containers based on domain boundaries
   - Containers should have minimal overlap in responsibilities
   - Functional decomposition should align with deployment and scaling needs

5. Identify and describe the users of the system.

6. Identify and create external systems as containers that interact with this system.
   For each external system identified:
   - Create a corresponding container representing the external system as a child node
   - Set ContainerType as "external"
   - Provide a suitable name
   - Describe the external system's purpose and role in the description
   - Leave other properties as there is no relevance.

7. Define External Interactions:
   - Containers and users
   - Containers and external systems

8. Generate a C4 System Context diagram using Mermaid syntax showing:
   - System boundaries using subgraphs
   - User types relevant to your specific system
   - External systems that your system actually needs to interact with
   - Relationships based on real system interactions
   - Protocols and data flows specific to your system

   C4 System Context Diagram Example - use this for reference only - generate diagrams based on the details of the users, external system identified for the project appropriately.

   IMPORTANT
The diagram syntax should be provided as raw Mermaid code without the keyword "mermaid" at the beginning and without any surrounding quotes. Start directly with 'graph TB'.
```
graph TB
    %% Users - Create based on discovered user types
    User1((Primary User))
    User2((Secondary User))
    
    %% System Context - Use actual system name and description
    subgraph SystemContext[Your System Name]
        direction TB
        System["Your System\nProvides core functionality\nImplements key features"]
    end
    
    %% External Systems - Based on actual integrations
    ExternalSystem1["External System 1\n[System: External]\nProvides specific service"]
    ExternalSystem2["External System 2\n[System: External]\nProvides specific service"]
    
    %% Relationships - Show actual data flows and protocols
    User1 -->|"Uses\nHTTP/REST"| System
    User2 -->|"Uses\nWebSocket"| System
    System -->|"Authenticates via\nOAuth2"| ExternalSystem1
    System -->|"Retrieves data\nREST API"| ExternalSystem2
    
    %% Styling
    classDef user fill:#08427b,stroke:#052e56,color:#ffffff
    classDef system fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef external fill:#666666,stroke:#333333,color:#ffffff
    
    class User1,User2 user
    class System system
    class ExternalSystem1,ExternalSystem2 external
```

9. Generate a C4 Container diagram showing:
    - All service containers with their primary responsibilities
    - Interfaces between containers
    - External system interactions
    - User interactions
    - Data flows and protocols

10. Define and Document All Interfaces:
    - For every container, user, and external system, explicitly define all interfaces.
    - For each interface, specify:
        - Source and target (container, user, or external system)
        - Interface type (REST, gRPC, GraphQL, etc.)
        - Protocol and data format
        - Description of the interface's purpose and usage
    - Ensure that no container is left without at least one interface.
    - Validate that all user and external system interactions are mapped to explicit interfaces.

{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

    C4 Container Diagram Example - use for reference only - generate diagrams based on the actual containers and interfaces identified for this project.
```
graph TB
    %% External Users
    Users((Users))
    
    %% System Containers
    subgraph SystemBoundary["System Name"]
        direction TB
        
        %% Containers
        Container1[Container 1\nDescription]
        Container2[Container 2\nDescription]
        Container3[Container 3\nDescription]
        
        %% Internal Container Relationships
        Container1 -->|"uses"| Container2
        Container2 -->|"uses"| Container3
    end
    
    %% External Systems
    ExternalSystem1[External System 1]
    ExternalSystem2[External System 2]
    
    %% External Relationships
    Users -->|"uses"| Container1
    Container3 -->|"uses"| ExternalSystem1
    Container2 -->|"uses"| ExternalSystem2
    
    %% Styling
    classDef user fill:#08427b,stroke:#052e56,color:#ffffff
    classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef external fill:#666666,stroke:#333333,color:#ffffff
    
    class Users user
    class Container1,Container2,Container3 container
    class ExternalSystem1,ExternalSystem2 external
```

Change Needed: 
   - Set to False if changes are not required.

Change Log:
   - Capture history of changes.
{% endblock %}

{% block autoconfig %}
Create a comprehensive C4 Model System Context based on the project's requirements, ensuring all functional and non-functional requirements are properly addressed in the system structure with multiple service containers.
{% endblock %}

{% block node_details_interactive_reconfig %}
Follow the above guidelines and propose the required changes based on your analysis.
Take feedback from the user on the proposal and consolidate. Make sure to capture all the changes in new child nodes or modified child nodes if the existing nodes do not include them.
{% endblock %}

{% block auto_reconfig %}
Create updated system context based on the above guidelines. Make sure to capture all the changes in new child nodes or modified child nodes if the existing nodes do not include them.
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
System context configuration, including multiple service containers, adhering to C4 model principles. Suggest improvements based on software architecture best practices and C4 modeling guidelines.
{% endblock %}

{% block information_about_task %}
{{ super() }}
    Existing interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}
    Existing Containers:
    {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
{{ super() }}
    Project Details: {{ details_for_discussion.get('project_details') }} 
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements', '') }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements', '') }}
    Existing Containers: {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Other Containers in the system: 
    {{ details_for_discussion.get('other_containers') | tojson(indent=2) }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %} 
