{% extends "base_discussion.prompt" %}

You are an expert software architect configuring a multi-container service system.

{% block task_description_common_preface %} 
{% if config_state != "configured" %}
As an expert software architect, design the multi-container architecture based on the C4 model:

{% else %}
You are an expert system architect reviewing the Multi-Container architecture for potential reconfiguration.
 
Note : Each container (node) should be treated as a separate repository, independently managed and deployed. 

1. Original Container Architecture:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Containers: {{ bg_info.get('system_context', {}).get('containers') | tojson(indent=2) }}
   - Interfaces: {{ bg_info.get('system_context', {}).get('interfaces') | tojson(indent=2) }}
   - Users: {{ bg_info.get('system_context', {}).get('users') | tojson(indent=2) }}
   - External Systems: {{ bg_info.get('system_context', {}).get('external_systems') | tojson(indent=2) }}

2. Current Architecture (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated containers: {{ new_bg.get('system_context', {}).get('containers') | tojson(indent=2) }}
   - Updated interfaces: {{ new_bg.get('system_context', {}).get('interfaces') | tojson(indent=2) }}
   - Users: {{ new_bg.get('system_context', {}).get('users') | tojson(indent=2) }}
   - External Systems: {{ new_bg.get('system_context', {}).get('external_systems') | tojson(indent=2) }}

3. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
4. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

Analysis Instructions:
1. Impact Analysis:
   - Compare original and new container architecture
   - Evaluate container responsibilities and boundaries
   - Analyze interface adequacy for new requirements
   
2. Required Changes:
   - Modify existing containers as needed based on the above analysis
   - IMPORTANT: Set the "changes_needed" flag to true if modifications are done else set to false.

{% endif %}

GUIDELINES:

1. Review Requirements Context:
   - Project Context: {{ details_for_discussion.get('project_details') | tojson(indent=2) }}
   - Users and External Systems:
     - Users: {{ details_for_discussion.get('users') | tojson(indent=2) }}
     - External Systems: {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
   - Requirements:
     - Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Multi-Container Architecture:
   - CRITICAL ARCHITECTURAL CONSTRAINTS: You MUST follow these rules strictly:
     * Create MULTIPLE containers as children for the current node
     * Each container should encapsulate a distinct set of functionality
     * Each container must be service-oriented with well-defined boundaries
     * Set the ContainerType as "internal" for each service container
   
   - Multi-Container Service Principles:
     * Each container should have a clearly defined purpose and responsibility
     * Each container should contain EXACTLY ONE component by default that implements all container functionality
     * Functionality should be distributed across containers based on domain boundaries
     * Containers should have minimal overlap in responsibilities
     * Functional decomposition should align with deployment and scaling needs

   - Container Properties:
     * Title: Clear, descriptive name. (*Do not include special characters in the Title).
     * Description: Purpose and responsibilities
     * ContainerType: Set to "internal" for service containers
     * Technology: Specify appropriate technology stack
     * For new Containers:
       * Use IDs like 'NEW-CONTAINER-1', 'NEW-CONTAINER-2', etc.

3. Define Interfaces Between Containers:
   - For each interface specify:
     * Interface type based on:
       - Communication patterns
       - Performance requirements
       - Reliability needs
     * Clear descriptive name
     * Detailed description
     * Source and target containers

4. Define and Validate All Interfaces:
    - List every interface between containers, users, and external systems.
    - For each interface, provide:
        - Name and type (REST, gRPC, etc.)
        - Source and target
        - Protocol and data format
        - Description of the interface's purpose
    - If any container does not have at least one provider or consumer interface, prompt for clarification and require correction.
    - Ensure the C4 Container diagram includes all interfaces, with clear protocol and data flow labels.

5. Define External Interactions:
   - Interfaces between containers and users
   - Interfaces between containers and external systems

6. Generate C4 Container Diagram:
   - Use Mermaid syntax
   - Show all containers
   - Include interfaces
   - Show relationships and dependencies

{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

   IMPORTANT: Use this diagram as a structural reference only. Generate your diagram based on:
   - The actual containers identified
   - The actual interfaces and relationships discovered
   - The specific technologies and protocols used
   - The external systems and users from the context

IMPORTANT
The diagram syntax should be provided as raw Mermaid code without the keyword "mermaid" at the beginning and without any surrounding quotes. Start directly with 'graph TB'.

   Multi-Container Service Container Diagram Reference:
```
graph TB
    %% External Users
    Users((Users))
    
    %% System Containers
    subgraph SystemBoundary["System Name"]
        direction TB
        
        %% Containers
        Container1[Container 1\nDescription]
        Container2[Container 2\nDescription]
        Container3[Container 3\nDescription]
        
        %% Internal Container Relationships
        Container1 -->|"uses"| Container2
        Container2 -->|"uses"| Container3
    end
    
    %% External Systems
    ExternalSystem1[External System 1]
    ExternalSystem2[External System 2]
    
    %% External Relationships
    Users -->|"uses"| Container1
    Container3 -->|"uses"| ExternalSystem1
    Container2 -->|"uses"| ExternalSystem2
    
    %% Styling
    classDef user fill:#08427b,stroke:#052e56,color:#ffffff
    classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef external fill:#666666,stroke:#333333,color:#ffffff
    
    class Users user
    class Container1,Container2,Container3 container
    class ExternalSystem1,ExternalSystem2 external
```

Note: When generating the diagram:
1. Replace generic names with actual container and system names
2. Use actual technologies in container descriptions
3. Show correct protocols in relationships
4. Include all identified containers, users, and external systems
5. Represent actual system boundaries and dependencies

Change Needed: 
    - Set to False if changes are not required.

Change Log:
    - Capture history of changes.

{% endblock %}

{% block autoconfig %}
Design a multi-container service architecture that distributes system functionality across multiple service containers with well-defined interfaces and responsibilities.
{% endblock %}

{% block node_details_interactive_reconfig %}
Follow the above guidelines to make the required changes to the multi-container architecture.
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
Multi-container service architecture configuration adhering to C4 model principles. Suggest improvements based on software architecture best practices and C4 modeling guidelines.
{% endblock %}

{% block information_about_task %}
    {{ super() }}
    Existing Containers:
    {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Existing interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}
    Other Containers in the system: 
    {{ details_for_discussion.get('other_containers') | tojson(indent=2) }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
    {{ super() }}
    Project Details: {{ details_for_discussion.get('project_details') }} 
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements', '') }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements', '') }}
    Existing Containers:
    {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Other Containers in the system: 
    {{ details_for_discussion.get('other_containers') | tojson(indent=2) }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %} 