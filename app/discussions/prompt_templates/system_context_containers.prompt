{% extends "base_discussion.prompt" %}

You are an expert software architect.

{% block task_description_common_preface %}

{% if config_state != "configured" %} 
As an expert software architect, design the internal container architecture based on the C4 model:
Note : Each container (node) should be treated as a separate repository, independently managed and deployed.

{% else %}
You are an expert system architect reviewing the Container architecture for potential reconfiguration.

1. Original Container Architecture:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Containers: {{ bg_info.get('containers', {}) | tojson(indent=2) }}
   - Interfaces: {{ bg_info.get('interfaces', {}) | tojson(indent=2) }}

2. Current Architecture (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated containers: {{ new_bg.get('containers', {}) | tojson(indent=2) }}
   - Updated interfaces: {{ new_bg.get('interfaces', {}) | toj<PERSON>(indent=2) }}

3. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}

{% endif %}

GUIDELINES:  

1. Review Requirements Context:
   - Project Context: {{ details_for_discussion.get('project_details') | tojson(indent=2) }}
   - Requirements:
     - Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Container Architecture:
   - Create containers as child nodes based on:
     * System responsibilities
     * Architecture strategy
     * Functional/Non-Functional requirements
   - For new Containers:
     * Use IDs like 'NEW-ARCH-1', 'NEW-ARCH-2', etc.
   - Container Properties:
     * Title: Clear, descriptive name. (*Do not include special characters in the title).
     * Description: Purpose and responsibilities
     * Type: Container
     * ContainerType: "internal" 

3. Interface Definition and Creation:
   - AUTOMATICALLY CREATE interface nodes as child nodes when containers need to communicate
   - For each interface node:
     * Create as a child node with unique ID (e.g., 'NEW-INTERFACE-1', 'NEW-INTERFACE-2')
     * Set Type: "Interface"
     * Properties to include:
       - Title: Clear, descriptive name
       - Description: Detailed description of the interface purpose
       - InterfaceType: Determine appropriate type based on:
         - Communication patterns (synchronous/asynchronous)
         - Performance requirements
         - Reliability needs
         - Integration constraints
       - Protocol: Specific protocol used
       - DataFormat: Data exchange format
       - ApiSignature: Examples of API calls or message formats

   Example interface creation scenarios:
   - Container-to-container communication requires REST API
   - Asynchronous processing needs Message Queue interface
   - Database access requires Database Interface
   - File operations need File Interface

4. Define interactions between containers through these interfaces

5. Generate C4 Container Diagram:
   - Use Mermaid syntax
   - Show all containers
   - Include interfaces
   - Show relationships and dependencies
   - While streaming the data stream the logic and not the actual mermaid syntax (To make it user friendly), use syntax only during tool calls.
   

{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

   IMPORTANT: Use this diagram as a structural reference only. Generate your diagram based on:
   - The containers identified in the system context
   - The actual interfaces and relationships discovered
   - The specific technologies and protocols used
   - The external systems including databases from the context

   Generic Container Diagram Reference:
```
graph TB
   %% External Users
   PrimaryUser["Primary User<br/>[Person]<br/><i>Main system user</i>"]
   AdminUser["Admin User<br/>[Person]<br/><i>System administrator</i>"]

   subgraph CoreSystem["Core System Containers"]
       %% Frontend Applications
       WebUI["Web Interface<br/>[Container: Frontend Tech]<br/><i>Main user interface</i>"]
       AdminUI["Admin Interface<br/>[Container: Frontend Tech]<br/><i>Administration interface</i>"]
       
       %% API Layer
       ApiGateway["API Gateway<br/>[Container: API Tech]<br/><i>Request routing and<br/>authentication</i>"]
       
       %% Core Services
       MainService["Main Service<br/>[Container: Backend Tech]<br/><i>Core business logic</i>"]
       
       SupportService["Support Service<br/>[Container: Backend Tech]<br/><i>Supporting functionality</i>"]
       
       %% Message Handling
       MessageBus["Message Bus<br/>[Container: Message Tech]<br/><i>Async communication</i>"]
   end

   %% External Systems
   PrimaryDB["Main Database<br/>[System_Ext]<br/><i>Primary data storage</i>"]
   CacheSystem["Cache Service<br/>[System_Ext]<br/><i>Data caching</i>"]
   AuthService["Auth System<br/>[System_Ext]<br/><i>Authentication</i>"]

   %% Relationships
   PrimaryUser -->|"Uses<br/>Protocol"| WebUI
   AdminUser -->|"Uses<br/>Protocol"| AdminUI
   
   WebUI -->|"API calls<br/>Protocol"| ApiGateway
   AdminUI -->|"API calls<br/>Protocol"| ApiGateway
   
   ApiGateway -->|"Routes<br/>Protocol"| MainService
   ApiGateway -->|"Routes<br/>Protocol"| SupportService
   
   MainService -->|"Reads/Writes<br/>Protocol"| PrimaryDB
   MainService -->|"Caches<br/>Protocol"| CacheSystem
   
   MainService -->|"Publishes<br/>Protocol"| MessageBus
   SupportService -->|"Subscribes<br/>Protocol"| MessageBus
   
   ApiGateway -->|"Authenticates<br/>Protocol"| AuthService

   %% Styling
   classDef person fill:#08427b,stroke:#052e56,color:#ffffff
   classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
   classDef external fill:#666666,stroke:#0b4884,color:#ffffff
   classDef boundary fill:none,stroke:#cccccc,stroke-dasharray:4

   class PrimaryUser,AdminUser person
   class WebUI,AdminUI,ApiGateway,MainService,SupportService,MessageBus container
   class PrimaryDB,CacheSystem,AuthService external
   class CoreSystem boundary
```

Note: When generating the diagram:
1. Replace generic names with actual container and system names
2. Use actual technologies in container descriptions
3. Show correct protocols in relationships
4. Include all identified containers and external systems
5. Represent actual system boundaries and dependencies

Change Needed: 
   - Set to False if changes are not required

Change Log:
   - Capture history of changes
{% endblock %}

{% block autoconfig %}
Design the internal container architecture focusing on containers and their interfaces.
{% endblock %}

{% block information_about_task %}
{{ super() }}
    Existing Containers:
    {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Existing interfaces: {{ details_for_discussion.get('interfaces') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
{{ super() }}
    Project Details: {{ details_for_discussion.get('project_details') }} 
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements', '') }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements', '') }}
    Existing Containers: {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Other Containers in the system: {{ details_for_discussion.get('other_containers') | tojson(indent=2) }}
{% endblock %} 