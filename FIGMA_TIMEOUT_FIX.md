# Figma Screen JSON Processing Timeout Fix

## Problem
The `writing_screen_json_files` endpoint was experiencing timeout issues where:
- API was not waiting for the response to complete
- Client received "No response in few seconds" error
- FastAPI logs showed the request was still processing in the background
- Long-running operations were blocking the HTTP response

## Root Cause
The endpoint was performing several time-consuming operations synchronously:
1. **Image processing** - Downloads and processes images from Figma API
2. **Screen processing** - Complex JSON processing with `create_screens_by_frame_ids_with_images`
3. **MongoDB operations** - Database updates
4. **File system operations** - Creating and writing JSON files
5. **Sync operations** - External API calls to sync attachments

These operations could take several minutes, exceeding typical HTTP timeout limits.

## Solution
Implemented **background task processing** with the following improvements:

### 1. Immediate Response Pattern
- Changed endpoint to return `202 Accepted` immediately
- Processing continues in background using FastAPI's `BackgroundTasks`
- Client gets immediate confirmation that processing has started

### 2. Background Processing with Timeouts
- Added `process_screen_json_files_background()` function
- Implemented overall timeout of 10 minutes using `asyncio.wait_for()`
- Added individual timeouts for each processing step:
  - Image processing: 2 minutes per screen
  - JSON creation: 5 minutes total
  - Sync operations: 1 minute

### 3. Progress Tracking via WebSocket
- Real-time progress updates sent via WebSocket
- Progress stages: LOADING_DATA → SETUP → PROCESSING_IMAGES → CREATING_JSON → UPDATING_DATABASE → SYNCING → COMPLETED
- Progress percentage from 0% to 100%

### 4. Status Monitoring Endpoint
- Added `GET /figma/screen_processing_status/{figma_id}` endpoint
- Allows clients to poll processing status
- Returns detailed information about:
  - Current processing status
  - Processing duration
  - Processed screen count
  - Error messages if any

### 5. Error Handling & Recovery
- Graceful timeout handling with proper status updates
- Individual screen processing failures don't stop the entire process
- MongoDB status updates even if processing fails
- WebSocket cleanup in finally blocks

## API Changes

### Modified Endpoint
```python
POST /figma/writing_screen_json_files
```
**Before**: Synchronous processing, could timeout
**After**: Returns immediately with 202 Accepted, processing continues in background

**New Response Format**:
```json
{
    "message": "Screen JSON file processing started",
    "status": "PROCESSING",
    "figma_id": "T1234-P5678-file123",
    "selected_screen_ids": ["1:123", "1:456"],
    "task_id": "task_789",
    "processing_started": true,
    "note": "Processing continues in background. Check status via WebSocket or polling endpoint."
}
```

### New Status Endpoint
```python
GET /figma/screen_processing_status/{figma_id}?project_id={project_id}
```

**Response Format**:
```json
{
    "figma_id": "T1234-P5678-file123",
    "project_id": "P5678",
    "processing_status": "COMPLETED",
    "processing_message": "Screen JSON files created successfully",
    "processing_started_at": "2024-01-15T10:30:00Z",
    "processing_completed_at": "2024-01-15T10:35:30Z",
    "duration_seconds": 330,
    "selected_screen_ids": ["1:123", "1:456"],
    "processed_screen_count": 2,
    "total_screen_count": 2,
    "task_id": "task_789",
    "is_complete": true,
    "screens_processed": [...]
}
```

## WebSocket Events
Clients can subscribe to `screen_processing_update` events for real-time updates:

```json
{
    "figma_id": "T1234-P5678-file123",
    "task_id": "task_789",
    "status": "PROCESSING_IMAGES",
    "progress": 45,
    "message": "Processing images for screen: Login Screen (2/4)",
    "timestamp": "2024-01-15T10:32:15Z"
}
```

## Benefits
1. **No more timeouts** - Immediate response prevents HTTP timeouts
2. **Better user experience** - Real-time progress updates
3. **Fault tolerance** - Individual failures don't stop entire process
4. **Monitoring** - Easy to track processing status
5. **Scalability** - Background processing doesn't block other requests

## Migration Notes
- Existing clients need to handle 202 response and implement status polling or WebSocket listening
- Processing results are now available via status endpoint or WebSocket events
- MongoDB schema includes new fields for processing status tracking

## Testing
Test the fix by:
1. Making a request to `/figma/writing_screen_json_files`
2. Verify immediate 202 response
3. Monitor progress via WebSocket or status endpoint
4. Confirm final completion status
