# Figma Screen JSON Processing Timeout Fix

## Problem
The `writing_screen_json_files` endpoint was experiencing timeout issues where:
- API was not waiting for the response to complete
- Client received "No response in few seconds" error
- FastAPI logs showed the request was still processing in the background
- Long-running operations were blocking the HTTP response

## Root Cause
The endpoint was performing several time-consuming operations synchronously:
1. **Image processing** - Downloads and processes images from Figma API
2. **Screen processing** - Complex JSON processing with `create_screens_by_frame_ids_with_images`
3. **MongoDB operations** - Database updates
4. **File system operations** - Creating and writing JSON files
5. **Sync operations** - External API calls to sync attachments

These operations could take several minutes, exceeding typical HTTP timeout limits.

## Solution
Implemented **60-second timeout handling** with granular timeouts for each operation:

### 1. Overall Timeout Protection
- Wrapped entire processing logic with `asyncio.wait_for(timeout=60.0)`
- Returns `408 Request Timeout` if processing exceeds 60 seconds
- Provides clear timeout message to client

### 2. Granular Operation Timeouts
- **Image processing**: 30 seconds per screen
- **Screen JSON creation**: 20 seconds total
- **Sync operations**: 10 seconds total
- Individual timeouts prevent any single operation from hanging

### 3. Graceful Error Handling
- Individual screen failures don't stop entire process
- Timeout errors are logged but processing continues
- Clear error messages for different failure scenarios

## API Changes

### Modified Endpoint Behavior
```python
POST /figma/writing_screen_json_files
```

**Before**: Could hang indefinitely, causing client timeouts
**After**: Guaranteed response within 60 seconds

**Success Response (200)**:
```json
{
    "message": "Screen JSON files created successfully",
    "screens_path": {...},
    "processed_screen_ids": ["1:123", "1:456"],
    "sync_response": {...},
    "design_system_path": "/path/to/design_system.json"
}
```

**Timeout Response (408)**:
```json
{
    "message": "Screen processing timed out after 60 seconds",
    "figma_id": "T1234-P5678-file123",
    "selected_screen_ids": ["1:123", "1:456"],
    "task_id": "task_789",
    "timeout_seconds": 60,
    "suggestion": "Try processing fewer screens at once or check Figma API connectivity"
}
```

## Implementation Details

### Timeout Structure
```python
# Main timeout wrapper (60 seconds)
result = await asyncio.wait_for(
    _process_screens_with_timeout(...),
    timeout=60.0
)

# Individual operation timeouts within the main function:
# - Image processing: 30s per screen
# - Screen creation: 20s total  
# - Sync operations: 10s total
```

### Error Recovery
- Image processing failures are logged but don't stop other screens
- Timeout on one screen doesn't affect others
- MongoDB updates continue even if some operations fail
- Partial success is still returned to client

## Benefits
1. **Predictable response times** - Maximum 60 seconds
2. **Better error handling** - Clear timeout vs other error types
3. **Partial success support** - Some screens can succeed even if others fail
4. **No hanging requests** - Guaranteed response within timeout
5. **Improved debugging** - Clear timeout messages and suggestions

## Testing
Test the fix by:
1. Making a request to `/figma/writing_screen_json_files` with multiple screens
2. Verify response comes within 60 seconds
3. Check that timeout responses include helpful error messages
4. Confirm partial processing works when some screens fail

## Migration Notes
- No breaking changes to request/response format
- Existing clients will now get faster, more reliable responses
- Timeout responses use HTTP 408 status code
- Error messages provide actionable suggestions
